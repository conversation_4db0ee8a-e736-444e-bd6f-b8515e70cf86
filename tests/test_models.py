"""
Test suite for data models
"""

import pytest
import sys
import os
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.agent import Agent, ToneType
from models.llm_config import LLMConfig, <PERSON><PERSON>rovider
from models.secret import Secret, APIKey


class TestAgent:
    """Test Agent model"""
    
    def test_agent_creation(self):
        """Test creating an agent"""
        agent = Agent(
            id="test_agent",
            name="Test Agent",
            description="A test agent",
            system_prompt="You are a helpful assistant",
            llm_config_name="test_config"
        )
        
        assert agent.id == "test_agent"
        assert agent.name == "Test Agent"
        assert agent.description == "A test agent"
        assert agent.system_prompt == "You are a helpful assistant"
        assert agent.llm_config_name == "test_config"
        assert agent.tone == ToneType.PROFESSIONAL
        assert agent.max_tokens == 1000
        assert agent.paraphrase_randomization == 0.0
    
    def test_agent_with_tools_and_capabilities(self):
        """Test agent with tools and capabilities"""
        agent = Agent(
            id="test_agent_2",
            name="Advanced Agent",
            description="An advanced test agent",
            system_prompt="You are an advanced assistant",
            tools=["calculator", "web_search"],
            capabilities=["math", "research"],
            tone=ToneType.CREATIVE,
            paraphrase_randomization=0.5,
            llm_config_name="advanced_config"
        )
        
        assert len(agent.tools) == 2
        assert "calculator" in agent.tools
        assert "web_search" in agent.tools
        assert len(agent.capabilities) == 2
        assert "math" in agent.capabilities
        assert "research" in agent.capabilities
        assert agent.tone == ToneType.CREATIVE
        assert agent.paraphrase_randomization == 0.5


class TestLLMConfig:
    """Test LLMConfig model"""
    
    def test_llm_config_creation(self):
        """Test creating an LLM configuration"""
        config = LLMConfig(
            id="test_config",
            provider=LLMProvider.GROQ,
            model_name="mixtral-8x7b-32768",
            secret_name="groq_secret"
        )
        
        assert config.id == "test_config"
        assert config.provider == LLMProvider.GROQ
        assert config.model_name == "mixtral-8x7b-32768"
        assert config.secret_name == "groq_secret"
        assert config.temperature == 0.7
    
    def test_ollama_config(self):
        """Test Ollama specific configuration"""
        config = LLMConfig(
            id="ollama_config",
            provider=LLMProvider.OLLAMA,
            model_name="llama2",
            base_url="http://localhost",
            port=11434
        )
        
        assert config.provider == LLMProvider.OLLAMA
        assert config.base_url == "http://localhost"
        assert config.port == 11434
        assert config.ollama_url == "http://localhost:11434"
    
    def test_ollama_url_property(self):
        """Test Ollama URL property"""
        config = LLMConfig(
            id="ollama_config",
            provider=LLMProvider.OLLAMA,
            model_name="llama2",
            base_url="http://*************",
            port=8080
        )
        
        assert config.ollama_url == "http://*************:8080"
        
        # Test without port
        config_no_port = LLMConfig(
            id="ollama_config_no_port",
            provider=LLMProvider.OLLAMA,
            model_name="llama2",
            base_url="http://localhost"
        )
        
        assert config_no_port.ollama_url == "http://localhost"


class TestSecret:
    """Test Secret model"""
    
    def test_secret_creation(self):
        """Test creating a secret"""
        secret = Secret(
            name="test_secret",
            provider="groq"
        )
        
        assert secret.name == "test_secret"
        assert secret.provider == "groq"
        assert secret.rotation_strategy == "round_robin"
        assert len(secret.api_keys) == 0
    
    def test_secret_with_api_keys(self):
        """Test secret with API keys"""
        secret = Secret(
            name="groq_secret",
            provider="groq"
        )
        
        secret.add_key("key1", "First Key")
        secret.add_key("key2", "Second Key")
        
        assert len(secret.api_keys) == 2
        assert secret.api_keys[0].key == "key1"
        assert secret.api_keys[0].name == "First Key"
        assert secret.api_keys[1].key == "key2"
        assert secret.api_keys[1].name == "Second Key"
    
    def test_key_rotation_round_robin(self):
        """Test round robin key rotation"""
        secret = Secret(
            name="test_secret",
            provider="test",
            rotation_strategy="round_robin"
        )
        
        secret.add_key("key1")
        secret.add_key("key2")
        secret.add_key("key3")
        
        # First call should return key1 (lowest usage count)
        key1 = secret.get_next_key()
        assert key1.key == "key1"
        assert key1.usage_count == 0
        
        # Use the key
        secret.use_key(key1)
        assert key1.usage_count == 1
        
        # Next call should return key2 (now lowest usage count)
        key2 = secret.get_next_key()
        assert key2.key == "key2"
        assert key2.usage_count == 0
    
    def test_key_rotation_random(self):
        """Test random key rotation"""
        secret = Secret(
            name="test_secret",
            provider="test",
            rotation_strategy="random"
        )
        
        secret.add_key("key1")
        secret.add_key("key2")
        
        # Should return one of the keys
        key = secret.get_next_key()
        assert key.key in ["key1", "key2"]
    
    def test_remove_key(self):
        """Test removing API keys"""
        secret = Secret(
            name="test_secret",
            provider="test"
        )
        
        secret.add_key("key1")
        secret.add_key("key2")
        
        assert len(secret.api_keys) == 2
        
        # Remove key1
        result = secret.remove_key("key1")
        assert result is True
        assert len(secret.api_keys) == 1
        assert secret.api_keys[0].key == "key2"
        
        # Try to remove non-existent key
        result = secret.remove_key("key3")
        assert result is False
        assert len(secret.api_keys) == 1


class TestAPIKey:
    """Test APIKey model"""
    
    def test_api_key_creation(self):
        """Test creating an API key"""
        api_key = APIKey(
            key="test_key_123",
            name="Test Key"
        )
        
        assert api_key.key == "test_key_123"
        assert api_key.name == "Test Key"
        assert api_key.usage_count == 0
        assert api_key.is_active is True
        assert api_key.last_used is None
        assert isinstance(api_key.created_at, datetime)


if __name__ == "__main__":
    pytest.main([__file__])
