"""
Pytest configuration and fixtures
"""

import pytest
import sys
import os

# Add src directory to path for all tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.fixture(scope="session")
def app():
    """Create QApplication for GUI tests"""
    from PyQt5.QtWidgets import QApplication
    
    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    yield app
    
    # Cleanup is handled automatically by Qt
