"""
Test suite for database operations
"""

import pytest
import sys
import os
import tempfile
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from database import Database
from models.agent import Agent, ToneType
from models.llm_config import LLMConfig, LLMProvider
from models.secret import Secret


class TestDatabase:
    """Test Database operations"""
    
    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        db = Database(db_path)
        yield db
        
        # Cleanup
        try:
            os.unlink(db_path)
        except OSError:
            pass
    
    def test_database_initialization(self, temp_db):
        """Test database initialization"""
        # Database should be created and tables should exist
        with temp_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['agents', 'llm_configs', 'secrets', 'api_keys']
            for table in expected_tables:
                assert table in tables
    
    def test_save_and_load_agent(self, temp_db):
        """Test saving and loading agents"""
        agent = Agent(
            id="test_agent",
            name="Test Agent",
            description="A test agent",
            system_prompt="You are helpful",
            llm_config_name="test_config",
            tools=["calculator"],
            capabilities=["math"]
        )
        
        # Save agent
        result = temp_db.save_agent(agent)
        assert result is True
        
        # Load agents
        loaded_agents = temp_db.load_agents()
        assert len(loaded_agents) == 1
        
        loaded_agent = loaded_agents[0]
        assert loaded_agent.id == agent.id
        assert loaded_agent.name == agent.name
        assert loaded_agent.description == agent.description
        assert loaded_agent.system_prompt == agent.system_prompt
        assert loaded_agent.llm_config_name == agent.llm_config_name
        assert loaded_agent.tools == agent.tools
        assert loaded_agent.capabilities == agent.capabilities
    
    def test_save_and_load_llm_config(self, temp_db):
        """Test saving and loading LLM configurations"""
        config = LLMConfig(
            id="test_config",
            provider=LLMProvider.GROQ,
            model_name="mixtral-8x7b-32768",
            secret_name="groq_secret",
            temperature=0.8
        )
        
        # Save config
        result = temp_db.save_llm_config(config, "Test Config")
        assert result is True
        
        # Load configs
        loaded_configs = temp_db.load_llm_configs()
        assert len(loaded_configs) == 1
        
        loaded_config = loaded_configs[0]
        assert loaded_config['id'] == config.id
        assert loaded_config['name'] == "Test Config"
        assert loaded_config['provider'] == config.provider
        assert loaded_config['model_name'] == config.model_name
        assert loaded_config['secret_name'] == config.secret_name
        assert loaded_config['temperature'] == config.temperature
    
    def test_save_and_load_secret(self, temp_db):
        """Test saving and loading secrets"""
        secret = Secret(
            name="test_secret",
            provider="groq",
            rotation_strategy="round_robin"
        )
        
        secret.add_key("key1", "First Key")
        secret.add_key("key2", "Second Key")
        
        # Save secret
        result = temp_db.save_secret(secret)
        assert result is True
        
        # Load secrets
        loaded_secrets = temp_db.load_secrets()
        assert len(loaded_secrets) == 1
        
        loaded_secret = loaded_secrets[0]
        assert loaded_secret.name == secret.name
        assert loaded_secret.provider == secret.provider
        assert loaded_secret.rotation_strategy == secret.rotation_strategy
        assert len(loaded_secret.api_keys) == 2
        
        # Check API keys
        assert loaded_secret.api_keys[0].key == "key1"
        assert loaded_secret.api_keys[0].name == "First Key"
        assert loaded_secret.api_keys[1].key == "key2"
        assert loaded_secret.api_keys[1].name == "Second Key"
    
    def test_delete_agent(self, temp_db):
        """Test deleting agents"""
        agent = Agent(
            id="test_agent",
            name="Test Agent",
            description="A test agent",
            system_prompt="You are helpful",
            llm_config_name="test_config"
        )
        
        # Save agent
        temp_db.save_agent(agent)
        
        # Verify it exists
        agents = temp_db.load_agents()
        assert len(agents) == 1
        
        # Delete agent
        result = temp_db.delete_agent("test_agent")
        assert result is True
        
        # Verify it's gone
        agents = temp_db.load_agents()
        assert len(agents) == 0
        
        # Try to delete non-existent agent
        result = temp_db.delete_agent("non_existent")
        assert result is False
    
    def test_delete_llm_config(self, temp_db):
        """Test deleting LLM configurations"""
        config = LLMConfig(
            id="test_config",
            provider=LLMProvider.GROQ,
            model_name="test_model"
        )
        
        # Save config
        temp_db.save_llm_config(config, "Test Config")
        
        # Verify it exists
        configs = temp_db.load_llm_configs()
        assert len(configs) == 1
        
        # Delete config
        result = temp_db.delete_llm_config("test_config")
        assert result is True
        
        # Verify it's gone
        configs = temp_db.load_llm_configs()
        assert len(configs) == 0
    
    def test_delete_secret(self, temp_db):
        """Test deleting secrets"""
        secret = Secret(
            name="test_secret",
            provider="test"
        )
        
        # Save secret
        temp_db.save_secret(secret)
        
        # Verify it exists
        secrets = temp_db.load_secrets()
        assert len(secrets) == 1
        
        # Delete secret
        result = temp_db.delete_secret("test_secret")
        assert result is True
        
        # Verify it's gone
        secrets = temp_db.load_secrets()
        assert len(secrets) == 0


if __name__ == "__main__":
    pytest.main([__file__])
