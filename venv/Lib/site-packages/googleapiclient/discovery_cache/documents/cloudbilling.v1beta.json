{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-billing": {"description": "View and manage your Google Cloud Platform billing accounts"}, "https://www.googleapis.com/auth/cloud-billing.readonly": {"description": "View your Google Cloud Platform billing accounts"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://cloudbilling.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloudbilling", "description": "Allows developers to manage billing for their Google Cloud Platform projects programmatically.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/billing/docs/apis", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudbilling:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudbilling.mtls.googleapis.com/", "name": "cloudbilling", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"billingAccounts": {"methods": {"estimateCostScenario": {"deprecated": true, "description": "Use custom pricing in the estimate, using a `CostScenario` with a defined `billingAccount`.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}:estimateCostScenario", "httpMethod": "POST", "id": "cloudbilling.billingAccounts.estimateCostScenario", "parameterOrder": ["billingAccount"], "parameters": {"billingAccount": {"description": "Resource name of the billing account for the cost estimate. The resource name has the form `billingAccounts/{billing_account_id}`. For example, `billingAccounts/012345-567890-ABCDEF` is the resource name for billing account `012345-567890-ABCDEF`. Must be specified.", "location": "path", "pattern": "^billingAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+billingAccount}:estimateCostScenario", "request": {"$ref": "EstimateCostScenarioForBillingAccountRequest"}, "response": {"$ref": "EstimateCostScenarioForBillingAccountResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"services": {"methods": {"get": {"description": "Gets a Google Cloud service visible to a billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/services/{servicesId}", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.services.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the billing account service to retrieve. Format: billingAccounts/{billing_account}/services/{service}", "location": "path", "pattern": "^billingAccounts/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingBillingaccountservicesV1betaBillingAccountService"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists services visible to a billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/services", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.services.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of billing account service to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous ListBillingAccountServices call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The billing account to list billing account service from. Format: billingAccounts/{billing_account}", "location": "path", "pattern": "^billingAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/services", "response": {"$ref": "GoogleCloudBillingBillingaccountservicesV1betaListBillingAccountServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}, "skuGroups": {"methods": {"get": {"description": "Gets a SKU group visible to a billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skuGroups/{skuGroupsId}", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skuGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the BillingAccountSkuGroup to retrieve. Format: billingAccounts/{billing_account}/skuGroups/{sku_group}", "location": "path", "pattern": "^billingAccounts/[^/]+/skuGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingBillingaccountskugroupsV1betaBillingAccountSkuGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists SKU groups visible to a billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skuGroups", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skuGroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of billing account SKU groups to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous ListBillingAccountSkuGroups call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The billing account to list billing account SKU groups from. Format: billingAccounts/{billing_account}", "location": "path", "pattern": "^billingAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/skuGroups", "response": {"$ref": "GoogleCloudBillingBillingaccountskugroupsV1betaListBillingAccountSkuGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"skus": {"methods": {"get": {"description": "Gets a SKU that is part of a billing account SKU group.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skuGroups/{skuGroupsId}/skus/{skusId}", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skuGroups.skus.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the billing account SKU group SKU to retrieve. Format: billingAccounts/{billing_account}/skuGroups/{sku_group}/skus/{sku}", "location": "path", "pattern": "^billingAccounts/[^/]+/skuGroups/[^/]+/skus/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaBillingAccountSkuGroupSku"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists SKUs that is part of billing account SKU groups.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skuGroups/{skuGroupsId}/skus", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skuGroups.skus.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of billing account SKU group SKUs to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous ListBillingAccountSkuGroupSkus call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The billing account SKU group to list billing account SKU group SKUs from. Format: billingAccounts/{billing_account}/skuGroups/{sku_group}", "location": "path", "pattern": "^billingAccounts/[^/]+/skuGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/skus", "response": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaListBillingAccountSkuGroupSkusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}}, "skus": {"methods": {"get": {"description": "Gets a SKU visible to a billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skus/{skusId}", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skus.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the billing account SKU to retrieve. Format: billingAccounts/{billing_account}/skus/{sku}", "location": "path", "pattern": "^billingAccounts/[^/]+/skus/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaBillingAccountSku"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists SKUs visible to a billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skus", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skus.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Options for how to filter the billing account SKUs. Currently, only filter on `billing_account_service` is supported. Only !=, = operators are supported. Examples: - billing_account_service = \"billingAccounts/012345-567890-ABCDEF/services/DA34-426B-A397\"", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of billing account SKUs to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous ListBillingAccountSkus call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The billing account to list billing account SKU from. Format: billingAccounts/{billing_account}", "location": "path", "pattern": "^billingAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/skus", "response": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaListBillingAccountSkusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"price": {"methods": {"get": {"description": "Gets the latest price for SKUs available to your Cloud Billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skus/{skusId}/price", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skus.price.get", "parameterOrder": ["name"], "parameters": {"currencyCode": {"description": "Optional. ISO-4217 currency code for the price. If not specified, the currency of the billing account is used.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the billing account price to retrieve. Format: billingAccounts/{billing_account}/skus/{sku}/price", "location": "path", "pattern": "^billingAccounts/[^/]+/skus/[^/]+/price$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaBillingAccountPrice"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}, "prices": {"methods": {"list": {"description": "Lists the latest prices for SKUs available to your Cloud Billing account.", "flatPath": "v1beta/billingAccounts/{billingAccountsId}/skus/{skusId}/prices", "httpMethod": "GET", "id": "cloudbilling.billingAccounts.skus.prices.list", "parameterOrder": ["parent"], "parameters": {"currencyCode": {"description": "Optional. ISO-4217 currency code for the price. If not specified, currency of billing account will be used.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of billing account price to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Page token received from a previous ListBillingAccountPrices call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. To list all Billing Account SKUs, use `-` as the SKU ID. Format: `billingAccounts/{billing_account}/skus/-` Note: Specifying an actual SKU resource id will return a collection of one Billing Account Price.", "location": "path", "pattern": "^billingAccounts/[^/]+/skus/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/prices", "response": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaListBillingAccountPricesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "skuGroups": {"methods": {"get": {"description": "Gets a publicly listed SKU group.", "flatPath": "v1beta/skuGroups/{skuGroupsId}", "httpMethod": "GET", "id": "cloudbilling.skuGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SKU group to retrieve. Format: skuGroups/{sku_group}", "location": "path", "pattern": "^skuGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingSkugroupsV1betaSkuGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all publicly listed SKU groups.", "flatPath": "v1beta/skuGroups", "httpMethod": "GET", "id": "cloudbilling.skuGroups.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "Maximum number of SKU groups to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous ListSkuGroups call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}}, "path": "v1beta/skuGroups", "response": {"$ref": "GoogleCloudBillingSkugroupsV1betaListSkuGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"skus": {"methods": {"get": {"description": "Gets a publicly listed SKU that is part of a publicly listed SKU group.", "flatPath": "v1beta/skuGroups/{skuGroupsId}/skus/{skusId}", "httpMethod": "GET", "id": "cloudbilling.skuGroups.skus.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SKU group SKU to retrieve. Format: skuGroups/{sku_group}/skus/{sku}", "location": "path", "pattern": "^skuGroups/[^/]+/skus/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingSkugroupskusV1betaSkuGroupSku"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all publicly listed SKUs contained by a publicly listed SKU group.", "flatPath": "v1beta/skuGroups/{skuGroupsId}/skus", "httpMethod": "GET", "id": "cloudbilling.skuGroups.skus.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of SKU group SKUs to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous ListSkuGroupSkus call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The SkuGroup to list SkuGroupSku from. Format: skuGroups/{sku_group}", "location": "path", "pattern": "^skuGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/skus", "response": {"$ref": "GoogleCloudBillingSkugroupskusV1betaListSkuGroupSkusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}}, "skus": {"resources": {"price": {"methods": {"get": {"description": "Gets the latest price for the given SKU.", "flatPath": "v1beta/skus/{skusId}/price", "httpMethod": "GET", "id": "cloudbilling.skus.price.get", "parameterOrder": ["name"], "parameters": {"currencyCode": {"description": "Optional. ISO-4217 currency code for the price. If not specified, USD will be used.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the latest price to retrieve. Format: skus/{sku}/price", "location": "path", "pattern": "^skus/[^/]+/price$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleCloudBillingPricesV1betaPrice"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}, "prices": {"methods": {"list": {"description": "Lists the latest prices for all SKUs.", "flatPath": "v1beta/skus/{skusId}/prices", "httpMethod": "GET", "id": "cloudbilling.skus.prices.list", "parameterOrder": ["parent"], "parameters": {"currencyCode": {"description": "Optional. ISO-4217 currency code for the price. If not specified, USD will be used.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of prices to return. Results may return fewer than this value. Default value is 50 and maximum value is 5000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Page token received from a previous ListPrices call to retrieve the next page of results. If this field is empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. To list the prices for all SKUs, use `-` as the SKU ID. Format: `skus/-` Specifying a specific SKU ID returns a collection with one Price object for the SKU.", "location": "path", "pattern": "^skus/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/prices", "response": {"$ref": "GoogleCloudBillingPricesV1betaListPricesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}}, "v1beta": {"deprecated": true, "methods": {"estimateCostScenario": {"deprecated": true, "description": "Estimate list prices using a `CostScenario` without a defined `billingAccount`.", "flatPath": "v1beta:estimateCostScenario", "httpMethod": "POST", "id": "cloudbilling.estimateCostScenario", "parameterOrder": [], "parameters": {}, "path": "v1beta:estimateCostScenario", "request": {"$ref": "EstimateCostScenarioWithListPriceRequest"}, "response": {"$ref": "EstimateCostScenarioWithListPriceResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-billing", "https://www.googleapis.com/auth/cloud-billing.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "********", "rootUrl": "https://cloudbilling.googleapis.com/", "schemas": {"CacheFillRegions": {"description": "Specifies the regions for Cache Fill.", "id": "CacheFillRegions", "properties": {"destinationRegion": {"description": "The destination region for cache fill.", "enum": ["CACHE_FILL_DESTINATION_REGION_UNSPECIFIED", "CACHE_FILL_DESTINATION_REGION_ASIA_PACIFIC", "CACHE_FILL_DESTINATION_REGION_EUROPE", "CACHE_FILL_DESTINATION_REGION_NORTH_AMERICA", "CACHE_FILL_DESTINATION_REGION_OCEANIA", "CACHE_FILL_DESTINATION_REGION_SOUTH_AMERICA", "CACHE_FILL_DESTINATION_REGION_CHINA", "CACHE_FILL_DESTINATION_REGION_OTHERS"], "enumDescriptions": ["Not specified", "Asia Pacific", "Europe", "North America", "Oceania", "South America", "China", "Others"], "type": "string"}, "sourceRegion": {"description": "The source region for cache fill.", "enum": ["CACHE_FILL_SOURCE_REGION_UNSPECIFIED", "CACHE_FILL_REGION_ASIA_PACIFIC", "CACHE_FILL_SOURCE_REGION_EUROPE", "CACHE_FILL_SOURCE_REGION_NORTH_AMERICA", "CACHE_FILL_SOURCE_REGION_OCEANIA", "CACHE_FILL_SOURCE_REGION_SOUTH_AMERICA"], "enumDescriptions": ["Not specified", "Asia Pacific", "Europe", "North America", "Oceania", "South America"], "type": "string"}}, "type": "object"}, "CloudCdnEgressWorkload": {"description": "Specifies usage for Cloud CDN Data Transfer.", "id": "CloudCdnEgressWorkload", "properties": {"cacheEgressDestination": {"description": "The destination for the cache data transfer.", "enum": ["CACHE_EGRESS_DESTINATION_UNSPECIFIED", "CACHE_EGRESS_DESTINATION_ASIA_PACIFIC", "CACHE_EGRESS_DESTINATION_CHINA", "CACHE_EGRESS_DESTINATION_EUROPE", "CACHE_EGRESS_DESTINATION_NORTH_AMERICA", "CACHE_EGRESS_DESTINATION_OCEANIA", "CACHE_EGRESS_DESTINATION_LATIN_AMERICA", "CACHE_EGRESS_DESTINATION_OTHER_DESTINATIONS"], "enumDescriptions": ["Unspecified.", "Asia Pacific.", "China.", "Europe.", "North America.", "Oceania including Australia, New Zealand, and surrounding Pacific Ocean islands such as Papua New Guinea and Fiji. This region excludes Hawaii.", "Latin America (Including the Caribbean, South America and Central America.)", "All other destinations (including Africa and Antarctica)"], "type": "string"}, "cacheEgressRate": {"$ref": "Usage", "description": "Cache data transfer usage. The rate of data cache transferred to the destination. Use units such as GiBy/s or TiBy/mo, based on [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard."}}, "type": "object"}, "CloudCdnWorkload": {"description": "Specifies usage for Cloud CDN resources.", "id": "CloudCdnWorkload", "properties": {"cacheFillOriginService": {"description": "The source service for the cache fill.", "enum": ["CACHE_FILL_ORIGIN_SERVICE_UNSPECIFIED", "CACHE_FILL_ORIGIN_SERVICE_GOOGLE_CLOUD_STORAGE_BUCKET", "CACHE_FILL_ORIGIN_SERVICE_BACKEND_SERVICE"], "enumDescriptions": ["Not specified.", "Origin service is Google Cloud Storage.", "Origin service is backend service, such as Compute VMs, external backend, etc."], "type": "string"}, "cacheFillRate": {"$ref": "Usage", "description": "Cache fill usage. The rate of data transferred between cache fill regions. For example: units such as \"GiBy/s\" or \"TBy/mo\"."}, "cacheFillRegions": {"$ref": "CacheFillRegions", "description": "The regions where data is transferred from Google data locations into Google global cache servers. The SKU prices for cache fill across services are the same."}, "cacheLookUpRate": {"$ref": "Usage", "description": "Cache look up requests. This is specified to indicate the number of requests. For example: units such as \"1/s\"."}}, "type": "object"}, "CloudInterconnectEgressWorkload": {"description": "Includes the estimate for Interconnect Data Transfer only. To specify usage for data transfer between VMs and internet end-points, use the Standard Tier Internet Data Transfer interface.", "id": "CloudInterconnectEgressWorkload", "properties": {"egressRate": {"$ref": "Usage", "description": "Outbound data transfer usage. This usage applies when you move or copy data from one Google Cloud service to another service. The units are GiBy/s, By/s, and so on, based on [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard."}, "interconnectConnectionLocation": {"description": "Locations in the [Interconnect connection location table](https://cloud.google.com/vpc/network-pricing#interconnect-pricing). These are the Interconnect Data Transfer charges.", "enum": ["INTERCONNECT_CONNECTION_LOCATION_UNSPECIFIED", "INTERCONNECT_CONNECTION_LOCATION_ASIA", "INTERCONNECT_CONNECTION_LOCATION_EUROPE", "INTERCONNECT_CONNECTION_LOCATION_NORTH_AMERICA", "INTERCONNECT_CONNECTION_LOCATION_SOUTH_AMERICA", "INTERCONNECT_CONNECTION_LOCATION_AUSTRALIA"], "enumDescriptions": ["Unspecified.", "Asia.", "Europe.", "North America.", "South America.", "Australia."], "type": "string"}}, "type": "object"}, "CloudInterconnectWorkload": {"description": "Specifies usage for Cloud Interconnect resources.", "id": "CloudInterconnectWorkload", "properties": {"interconnectAttachments": {"description": "VLAN attachment used for interconnect.", "items": {"$ref": "VlanAttachment"}, "type": "array"}, "interconnectType": {"description": "VLAN attachment type", "enum": ["INTERCONNECT_TYPE_UNSPECIFIED", "INTERCONNECT_TYPE_DEDICATED", "INTERCONNECT_TYPE_PARTNER"], "enumDescriptions": ["Unspecified", "Type is dedicated", "Type is partner"], "type": "string"}, "linkType": {"description": "Interconnect circuit link type.", "enum": ["LINK_TYPE_UNSPECIFIED", "LINK_TYPE_ETHERNET_10G_LR", "LINK_TYPE_ETHERNET_100G_LR"], "enumDescriptions": ["Unspecified", "Link type is 10 Gbps.", "Link type is 100 Gbps."], "type": "string"}, "provisionedLinkCount": {"$ref": "Usage", "description": "Interconnect usage. This is specified as a unitless quantity which indicates the number of circuit provisioned in interconnect."}}, "type": "object"}, "CloudStorageEgressWorkload": {"description": "Specification of a network type. Network data transfer within Google Cloud applies when you move or copy data from one Cloud Storage bucket to another or when another Google Cloud service accesses data in your Cloud Storage bucket.This includes the network data transfer within Google Cloud and the general network usage. * If transferring data between two regions, the source and destination fields are set to different values. For example: `source_continent` = \"SOURCE_CONTINENT_ASIA_PACIFIC\", `destination_continent` = \"SOURCE_CONTINENT_SOUTH_AMERICA\". * If transferring data within one region, the source and destination fields are set to the same value. For example: `source_continent` = \"SOURCE_CONTINENT_ASIA_PACIFIC\", `destination_continent` = \"SOURCE_CONTINENT_ASIA_PACIFIC\". Some examples for the Network data transfer traffic type on the pricing page. * Data moves between different locations on the same continent. `source_continent` = \"SOURCE_CONTINENT_ASIA_PACIFIC\", `destination_continent` = \"SOURCE_CONTINENT_ASIA_PACIFIC\". * Data moves between different continents and neither is Australia. `source_continent` = \"SOURCE_CONTINENT_NORTH_AMERICA\", `destination_continent` = \"SOURCE_CONTINENT_ASIA_PACIFIC\". * Data moves between different continents and one is Australia. `source_continent` = \"SOURCE_CONTINENT_NORTH_AMERICA\", `destination_continent` = \"SOURCE_CONTINENT_AUSTRALIA\".", "id": "CloudStorageEgressWorkload", "properties": {"destinationContinent": {"description": "Where the data is sent to.", "enum": ["DESTINATION_CONTINENT_UNSPECIFIED", "DESTINATION_CONTINENT_ASIA_PACIFIC", "DESTINATION_CONTINENT_AUTRALIA", "DESTINATION_CONTINENT_EUROPE", "DESTINATION_CONTINENT_NORTH_AMERICA", "DESTINATION_CONTINENT_SOUTH_AMERICA"], "enumDescriptions": ["Not specified.", "Asia Pacific.", "Australia.", "Europe.", "North America.", "South America"], "type": "string"}, "egressRate": {"$ref": "Usage", "description": "Data transfer usage rate. This usage applies when you move or copy data from one Cloud Storage bucket to another or when another Google Cloud service accesses data in your Cloud Storage bucket. The expected units are GiBy/s, By/s, and so on, based on [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard."}, "sourceContinent": {"description": "Where the data comes from.", "enum": ["SOURCE_CONTINENT_UNSPECIFIED", "SOURCE_CONTINENT_ASIA_PACIFIC", "SOURCE_CONTINENT_AUSTRALIA", "SOURCE_CONTINENT_EUROPE", "SOURCE_CONTINENT_NORTH_AMERICA", "SOURCE_CONTINENT_SOUTH_AMERICA"], "enumDescriptions": ["Not specified.", "Asia Pacific.", "Australia.", "Europe.", "North America.", "South America."], "type": "string"}}, "type": "object"}, "CloudStorageWorkload": {"description": "Specifies usage of Cloud Storage resources.", "id": "CloudStorageWorkload", "properties": {"dataRetrieval": {"$ref": "Usage", "description": "Data retrieval usage. A retrieval cost applies when data or metadata is read, copied, or rewritten . For example: units such as \"GiB/s\" or \"B/s\"."}, "dataStored": {"$ref": "Usage", "description": "Data storage usage. The amount of data stored in buckets. For example: units such as GiBy/s or TiBy/mo, based on [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard."}, "dualRegion": {"$ref": "DualRegional", "description": "Specify dual regions."}, "multiRegion": {"$ref": "MultiRegional", "description": "Specify multi regions."}, "operationA": {"$ref": "Usage", "description": "Class A operation usage in Cloud Storage, such as listing the objects in buckets. See the [operations pricing](https://cloud.google.com/storage/pricing#operations-pricing) tables for a list of which operations fall into each class. For example: units such as \"1/s\"."}, "operationB": {"$ref": "Usage", "description": "Class B operation usage in Cloud Storage, such as `getIamPolicy`. See the [operations pricing](https://cloud.google.com/storage/pricing#operations-pricing) tables for a list of which operations fall into each class. For example: units such as \"1/s\"."}, "region": {"$ref": "Regional", "description": "Specify a single region."}, "storageClass": {"description": "The [storage class](https://cloud.google.com/storage/docs/storage-classes#classes) of the data and operation. For example: \"standard\" and \"nearline\".", "type": "string"}}, "type": "object"}, "Commitment": {"description": "Commitments give you the ability to pay a recurring fee in exchange for a benefit, such as a discount for your use. For example, this object might contain details of a [spend-based committed use discount (CUD)](https://cloud.google.com/docs/cuds#spend_based_commitments). Within a CostScenario, adding a commitment includes the cost of the commitment and any discounts.", "id": "Commitment", "properties": {"name": {"description": "Required. A name for this commitment. All commitments in a CostScenario must have unique names. Each name may be at most 128 characters long.", "type": "string"}, "vmResourceBasedCud": {"$ref": "VmResourceBasedCud", "description": "A resource-based committed use discount (CUD)."}}, "type": "object"}, "CommitmentCostEstimate": {"description": "Estimated cost for a commitment.", "id": "CommitmentCostEstimate", "properties": {"commitmentTotalCostEstimate": {"$ref": "CostEstimate", "description": "Total estimated costs for the commitment."}, "name": {"description": "The name of the commitment, as specified in the `CostScenario`.", "type": "string"}, "skuCostEstimates": {"description": "Estimated costs for each SKU in the commitment.", "items": {"$ref": "SkuCostEstimate"}, "type": "array"}}, "type": "object"}, "ComputeVmWorkload": {"description": "Specificies usage of a set of identical compute VM instances.", "id": "ComputeVmWorkload", "properties": {"enableConfidentialCompute": {"description": "Defines whether each instance has confidential compute enabled.", "type": "boolean"}, "guestAccelerator": {"$ref": "GuestAccelerator", "description": "Guest accelerators attached to each machine."}, "instancesRunning": {"$ref": "Usage", "description": "VM usage. This is specified as a unitless quantity which indicates the number of instances running."}, "licenses": {"description": "Premium image licenses used by each instance.", "items": {"type": "string"}, "type": "array"}, "machineType": {"$ref": "MachineType", "description": "The machine type."}, "persistentDisks": {"description": "Persistent disks attached to each instance. Must include a boot disk.", "items": {"$ref": "PersistentDisk"}, "type": "array"}, "preemptible": {"description": "Defines whether each instance is preemptible.", "type": "boolean"}, "region": {"description": "The [region](https://cloud.google.com/compute/docs/regions-zones) where the VMs run. For example: \"us-central1\".", "type": "string"}}, "type": "object"}, "CostEstimate": {"description": "An estimated cost.", "id": "CostEstimate", "properties": {"creditEstimates": {"description": "The estimated credits applied.", "items": {"$ref": "CreditEstimate"}, "type": "array"}, "netCostEstimate": {"$ref": "Money", "description": "The estimated net cost after applying credits."}, "preCreditCostEstimate": {"$ref": "Money", "description": "The estimated cost prior to applying credits."}}, "type": "object"}, "CostEstimationResult": {"description": "The result of a estimating the costs of a `CostScenario`.", "id": "CostEstimationResult", "properties": {"currencyCode": {"description": "Required. The ISO 4217 currency code for the cost estimate.", "type": "string"}, "segmentCostEstimates": {"description": "Required. Estimated costs for each idealized month of a `CostScenario`.", "items": {"$ref": "SegmentCostEstimate"}, "type": "array"}, "skus": {"description": "Required. Information about SKUs used in the estimate.", "items": {"$ref": "S<PERSON>"}, "type": "array"}}, "type": "object"}, "CostScenario": {"description": "Encapsulates all the information needed to perform a cost estimate. It includes a specification of the Google Cloud usage whose costs are estimated, and configuration options.", "id": "CostScenario", "properties": {"commitments": {"description": "New commitments to estimate the costs for. The cost of the commitments will be included in the estimate result and discounts the commitment entitles will be included in the workload cost estimates. A maximum of 100 workloads can be provided.", "items": {"$ref": "Commitment"}, "type": "array"}, "scenarioConfig": {"$ref": "ScenarioConfig", "description": "Configuration for the scenario."}, "workloads": {"description": "The Google Cloud usage whose costs are estimated. A maximum of 100 workloads can be provided.", "items": {"$ref": "Workload"}, "type": "array"}}, "type": "object"}, "CreditEstimate": {"description": "An estimated credit applied to the costs on a SKU.", "id": "CreditEstimate", "properties": {"creditAmount": {"$ref": "Money", "description": "The estimated credit amount."}, "creditDescription": {"description": "The credit description.", "type": "string"}, "creditType": {"description": "The credit type.", "type": "string"}}, "type": "object"}, "CustomMachineType": {"description": "Specification of a custom machine type.", "id": "CustomMachineType", "properties": {"machineSeries": {"description": "Required. The machine series. Only certain [machine series](https://cloud.google.com/compute/docs/general-purpose-machines#custom_machine_types) support custom configurations. For example: \"n1\".", "type": "string"}, "memorySizeGb": {"description": "Required. Memory size of the VM in GB (2^30 bytes). Must be an increment of 0.25 (256 MB). Each [machine series](https://cloud.google.com/compute/docs/machine-types#machine_type_comparison) has limitations on allowed values for the ratio of memory-to-vCPU count.", "format": "double", "type": "number"}, "virtualCpuCount": {"description": "Required. The number of vCPUs. The allowed values depend on the [machine series](https://cloud.google.com/compute/docs/machine-types#machine_type_comparison).", "format": "int64", "type": "string"}}, "type": "object"}, "Decimal": {"description": "A representation of a decimal value, such as 2.5. Clients may convert values into language-native decimal formats, such as Java's [BigDecimal](https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/math/BigDecimal.html) or Python's [decimal.Decimal](https://docs.python.org/3/library/decimal.html).", "id": "Decimal", "properties": {"value": {"description": "The decimal value, as a string. The string representation consists of an optional sign, `+` (`U+002B`) or `-` (`U+002D`), followed by a sequence of zero or more decimal digits (\"the integer\"), optionally followed by a fraction, optionally followed by an exponent. An empty string **should** be interpreted as `0`. The fraction consists of a decimal point followed by zero or more decimal digits. The string must contain at least one digit in either the integer or the fraction. The number formed by the sign, the integer and the fraction is referred to as the significand. The exponent consists of the character `e` (`U+0065`) or `E` (`U+0045`) followed by one or more decimal digits. Services **should** normalize decimal values before storing them by: - Removing an explicitly-provided `+` sign (`+2.5` -> `2.5`). - Replacing a zero-length integer value with `0` (`.5` -> `0.5`). - Coercing the exponent character to upper-case, with explicit sign (`2.5e8` -> `2.5E+8`). - Removing an explicitly-provided zero exponent (`2.5E0` -> `2.5`). Services **may** perform additional normalization based on its own needs and the internal decimal implementation selected, such as shifting the decimal point and exponent value together (example: `2.5E-1` <-> `0.25`). Additionally, services **may** preserve trailing zeroes in the fraction to indicate increased precision, but are not required to do so. Note that only the `.` character is supported to divide the integer and the fraction; `,` **should not** be supported regardless of locale. Additionally, thousand separators **should not** be supported. If a service does support them, values **must** be normalized. The ENBF grammar is: DecimalString = '' | [Sign] Significand [Exponent]; Sign = '+' | '-'; Significand = Digits '.' | [Digits] '.' Digits; Exponent = ('e' | 'E') [Sign] Digits; Digits = { '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' }; Services **should** clearly document the range of supported values, the maximum supported precision (total number of digits), and, if applicable, the scale (number of digits after the decimal point), as well as how it behaves when receiving out-of-bounds values. Services **may** choose to accept values passed as input even when the value has a higher precision or scale than the service supports, and **should** round the value to fit the supported scale. Alternatively, the service **may** error with `400 Bad Request` (`INVALID_ARGUMENT` in gRPC) if precision would be lost. Services **should** error with `400 Bad Request` (`INVALID_ARGUMENT` in gRPC) if the service receives a value outside of the supported range.", "type": "string"}}, "type": "object"}, "DualRegional": {"description": "Area contains dual locations.", "id": "DualRegional", "properties": {"name": {"description": "The [location name](https://cloud.google.com/storage/docs/locations#available-locations) where the data is stored. For example: \"asia1\" for dual region.", "type": "string"}}, "type": "object"}, "EstimateCostScenarioForBillingAccountRequest": {"description": "Request for EstimateCostScenarioForBillingAccount.", "id": "EstimateCostScenarioForBillingAccountRequest", "properties": {"costScenario": {"$ref": "CostScenario", "description": "The scenario to estimate costs for."}}, "type": "object"}, "EstimateCostScenarioForBillingAccountResponse": {"description": "Response for EstimateCostScenarioForBillingAccount", "id": "EstimateCostScenarioForBillingAccountResponse", "properties": {"costEstimationResult": {"$ref": "CostEstimationResult", "description": "The result of the cost estimation."}}, "type": "object"}, "EstimateCostScenarioWithListPriceRequest": {"description": "Request for EstimateCostScenarioWithListPrice.", "id": "EstimateCostScenarioWithListPriceRequest", "properties": {"costScenario": {"$ref": "CostScenario", "description": "The scenario to estimate costs for."}}, "type": "object"}, "EstimateCostScenarioWithListPriceResponse": {"description": "Response for EstimateCostScenarioWithListPrice", "id": "EstimateCostScenarioWithListPriceResponse", "properties": {"costEstimationResult": {"$ref": "CostEstimationResult", "description": "The result of the cost estimation."}}, "type": "object"}, "EstimationTimePoint": {"description": "Represents a point in time.", "id": "EstimationTimePoint", "properties": {"estimationTimeFrameOffset": {"description": "The point in time, relative to the start of the time frame covered by the cost estimate.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaAggregationInfo": {"description": "Encapsulates the aggregation information such as aggregation level and interval for a billing account price.", "id": "GoogleCloudBillingBillingaccountpricesV1betaAggregationInfo", "properties": {"interval": {"description": "Interval at which usage is aggregated to compute cost. Example: \"MONTHLY\" interval indicates that usage is aggregated every month.", "enum": ["INTERVAL_UNSPECIFIED", "INTERVAL_MONTHLY", "INTERVAL_DAILY"], "enumDescriptions": ["Default unspecified value.", "Usage is aggregated every month.", "Usage is aggregated every day."], "type": "string"}, "level": {"description": "Level at which usage is aggregated to compute cost. Example: \"ACCOUNT\" level indicates that usage is aggregated across all projects in a single account.", "enum": ["LEVEL_UNSPECIFIED", "LEVEL_ACCOUNT", "LEVEL_PROJECT"], "enumDescriptions": ["Default unspecified value.", "Usage is aggregated at an account level.", "Usage is aggregated at a project level."], "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaBillingAccountPrice": {"description": "Encapsulates the latest price for a billing account SKU.", "id": "GoogleCloudBillingBillingaccountpricesV1betaBillingAccountPrice", "properties": {"currencyCode": {"description": "ISO-4217 currency code for the price.", "type": "string"}, "name": {"description": "Resource name for the latest billing account price.", "type": "string"}, "priceReason": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaPriceReason", "description": "Background information on the origin of the price."}, "rate": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaRate", "description": "Rate price metadata. Billing account SKUs with `Rate` price are offered by pricing tiers. The price can have 1 or more rate pricing tiers."}, "valueType": {"description": "Type of the price. The possible values are: [\"unspecified\", \"rate\"].", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaDefaultPrice": {"description": "Encapsulates a default price which is the current list price.", "id": "GoogleCloudBillingBillingaccountpricesV1betaDefaultPrice", "properties": {}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaFixedDiscount": {"description": "Encapsulates a discount off the list price, anchored to the list price as of a fixed time.", "id": "GoogleCloudBillingBillingaccountpricesV1betaFixedDiscount", "properties": {"discountPercent": {"$ref": "Decimal", "description": "Percentage of the fixed discount."}, "discountScopeType": {"description": "Type of the fixed discount scope which indicates the source of the discount. It can have values such as 'unspecified' and 'sku-group'.", "type": "string"}, "fixTime": {"description": "Time that the fixed discount is anchored to.", "format": "google-datetime", "type": "string"}, "skuGroup": {"description": "SKU group where the fixed discount comes from.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaFixedPrice": {"description": "Encapsulates a set fixed price applicable during the terms of a contract agreement.", "id": "GoogleCloudBillingBillingaccountpricesV1betaFixedPrice", "properties": {}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaFloatingDiscount": {"description": "Encapsulates a discount off the current list price, not anchored to any list price as of a fixed time.", "id": "GoogleCloudBillingBillingaccountpricesV1betaFloatingDiscount", "properties": {"discountPercent": {"$ref": "Decimal", "description": "Percentage of the floating discount."}, "discountScopeType": {"description": "Type of the floating discount scope which indicates the source of the discount. It can have values such as 'unspecified' and 'sku-group'.", "type": "string"}, "skuGroup": {"description": "SKU group where the floating discount comes from.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaListBillingAccountPricesResponse": {"description": "Response message for ListBillingAccountPrices.", "id": "GoogleCloudBillingBillingaccountpricesV1betaListBillingAccountPricesResponse", "properties": {"billingAccountPrices": {"description": "The returned billing account prices.", "items": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaBillingAccountPrice"}, "type": "array"}, "nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaListPriceAsCeiling": {"description": "Encapsulates a contract feature that the list price (DefaultPrice) will be used for the price if the current list price drops lower than the custom fixed price. Available to new contracts after March 21, 2022. Applies to all fixed price SKUs in the contract, including FixedPrice, FixedDiscount, MigratedPrice, and MergedPrice.", "id": "GoogleCloudBillingBillingaccountpricesV1betaListPriceAsCeiling", "properties": {}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaMergedPrice": {"description": "Encapsulates a price after merging from multiple sources. With merged tiers, each individual tier can be from a different source with different discount types.", "id": "GoogleCloudBillingBillingaccountpricesV1betaMergedPrice", "properties": {}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaMigratedPrice": {"description": "Encapsulates a price migrated from other SKUs.", "id": "GoogleCloudBillingBillingaccountpricesV1betaMigratedPrice", "properties": {"sourceSku": {"description": "Source SKU where the discount is migrated from. Format: billingAccounts/{billing_account}/skus/{sku}", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaPriceReason": {"description": "Encapsulates a price reason which contains background information about the origin of the price.", "id": "GoogleCloudBillingBillingaccountpricesV1betaPriceReason", "properties": {"defaultPrice": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaDefaultPrice", "description": "Default price which is the current list price."}, "fixedDiscount": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaFixedDiscount", "description": "Discount off the list price, anchored to the list price as of a fixed time."}, "fixedPrice": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaFixedPrice", "description": "Fixed price applicable during the terms of a contract agreement."}, "floatingDiscount": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaFloatingDiscount", "description": "Discount off the current list price, not anchored to any list price as of a fixed time."}, "listPriceAsCeiling": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaListPriceAsCeiling", "description": "Contract feature that the list price (DefaultPrice) will be used for the price if the current list price drops lower than the custom fixed price. Available to new contracts after March 21, 2022. Applies to all fixed price SKUs in the contract, including FixedPrice, FixedDiscount, MigratedPrice, and MergedPrice."}, "mergedPrice": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaMergedPrice", "description": "Price after merging from multiple sources."}, "migratedPrice": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaMigratedPrice", "description": "Price migrated from other SKUs."}, "type": {"description": "Type of the price reason. It can have values such as 'unspecified', 'default-price', 'fixed-price', 'fixed-discount', 'floating-discount', 'migrated-price', 'merged-price', 'list-price-as-ceiling'.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaRate": {"description": "Encapsulates a `Rate` price. Billing account SKUs with `Rate` price are offered by pricing tiers. The price have 1 or more rate pricing tiers.", "id": "GoogleCloudBillingBillingaccountpricesV1betaRate", "properties": {"aggregationInfo": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaAggregationInfo", "description": "Aggregation info for tiers such as aggregation level and interval."}, "tiers": {"description": "All tiers associated with the `Rate` price.", "items": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaRateTier"}, "type": "array"}, "unitInfo": {"$ref": "GoogleCloudBillingBillingaccountpricesV1betaUnitInfo", "description": "Unit info such as name and quantity."}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaRateTier": {"description": "Encapsulates a rate price tier.", "id": "GoogleCloudBillingBillingaccountpricesV1betaRateTier", "properties": {"contractPrice": {"$ref": "Money", "description": "Negotiated contract price specific for a billing account."}, "effectiveDiscountPercent": {"$ref": "Decimal", "description": "Percentage of effective discount calculated using the current list price per pricing tier. Formula used: effective_discount_percent = (list_price - contract_price) / list_price × 100 If list_price and contract_price are zero, this field is the same as `discount_percent` of FixedDiscount and FloatingDiscount. If your contract does NOT have the feature LIST_PRICE_AS_CEILING enabled, the effective_discount_percent can be negative if the SKU has a FixedDiscount and the current list price is lower than the list price on the date of the contract agreement. See the `FixedDiscount.fix_time` on when the discount was set. If you have questions regarding pricing per SKU, contact your Account team for more details."}, "listPrice": {"$ref": "Money", "description": "List price of one tier."}, "startAmount": {"$ref": "Decimal", "description": "Lower bound amount for a tier. Tiers 0-100, 100-200 will be represented with two tiers with `start_amount` 0 and 100."}}, "type": "object"}, "GoogleCloudBillingBillingaccountpricesV1betaUnitInfo": {"description": "Encapsulates the unit information for a Rate", "id": "GoogleCloudBillingBillingaccountpricesV1betaUnitInfo", "properties": {"unit": {"description": "Shorthand for the unit. Example: GiBy.mo.", "type": "string"}, "unitDescription": {"description": "Human-readable description of the unit. Example: gibibyte month.", "type": "string"}, "unitQuantity": {"$ref": "Decimal", "description": "Unit quantity for the tier. Example: if the RateTier price is $1 per 1000000 Bytes, then `unit_quantity` is set to 1000000."}}, "type": "object"}, "GoogleCloudBillingBillingaccountservicesV1betaBillingAccountService": {"description": "Encapsulates a Google Cloud service visible to a billing account.", "id": "GoogleCloudBillingBillingaccountservicesV1betaBillingAccountService", "properties": {"displayName": {"description": "Description of the BillingAccountService. Example: \"BigQuery\", \"Compute Engine\".", "type": "string"}, "name": {"description": "Resource name for the BillingAccountService. Example: \"billingAccounts/012345-567890-ABCDEF/services/DA34-426B-A397\".", "type": "string"}, "serviceId": {"description": "Identifier for the service. It is the string after the collection identifier \"services/\". Example: \"DA34-426B-A397\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountservicesV1betaListBillingAccountServicesResponse": {"description": "Response message for ListBillingAccountServices.", "id": "GoogleCloudBillingBillingaccountservicesV1betaListBillingAccountServicesResponse", "properties": {"billingAccountServices": {"description": "The returned billing account services.", "items": {"$ref": "GoogleCloudBillingBillingaccountservicesV1betaBillingAccountService"}, "type": "array"}, "nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupsV1betaBillingAccountSkuGroup": {"description": "Encapsulates a stock keeping (SKU) group visible to a billing account. A SKU group represents a collection of SKUs that are related to each other. For example, the `AI Platform APIs` SKU group includes SKUs from the Cloud Dialogflow API, the Cloud Text-to-Speech API, and additional related APIs.", "id": "GoogleCloudBillingBillingaccountskugroupsV1betaBillingAccountSkuGroup", "properties": {"displayName": {"description": "Description of the BillingAccountSkuGroup. Example: \"A2 VMs (1 Year CUD)\".", "type": "string"}, "name": {"description": "Resource name for the BillingAccountSkuGroup. Example: \"billingAccounts/012345-567890-ABCDEF/skuGroups/0e6403d1-4694-44d2-a696-7a78b1a69301\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupsV1betaListBillingAccountSkuGroupsResponse": {"description": "Response message for ListBillingAccountSkuGroups.", "id": "GoogleCloudBillingBillingaccountskugroupsV1betaListBillingAccountSkuGroupsResponse", "properties": {"billingAccountSkuGroups": {"description": "The returned publicly listed billing account SKU groups.", "items": {"$ref": "GoogleCloudBillingBillingaccountskugroupsV1betaBillingAccountSkuGroup"}, "type": "array"}, "nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaBillingAccountSkuGroupSku": {"description": "Encapsulates a SKU that is part of a billing account SKU group.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaBillingAccountSkuGroupSku", "properties": {"billingAccountService": {"description": "BillingAccountService that the BillingAccountSkuGroupSku belongs to.", "type": "string"}, "displayName": {"description": "Description of the BillingAccountSkuGroupSku. Example: \"A2 Instance Core running in Hong Kong\".", "type": "string"}, "geoTaxonomy": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomy", "description": "Geographic metadata that applies to the BillingAccountSkuGroupSku."}, "name": {"description": "Resource name for the BillingAccountSkuGroupSku. Example: \"billingAccounts/012345-567890-ABCDEF/skuGroups/0e6403d1-4694-44d2-a696-7a78b1a69301/skus/AA95-CD31-42FE\".", "type": "string"}, "productTaxonomy": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaProductTaxonomy", "description": "List of product categories that apply to the BillingAccountSkuGroupSku."}, "skuId": {"description": "Unique identifier for the SKU. It is the string after the collection identifier \"skus/\" Example: \"AA95-CD31-42FE\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomy": {"description": "Encapsulates geographic metadata, such as regions and multi-regions like `us-east4` or `European Union`.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomy", "properties": {"globalMetadata": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyGlobal", "description": "Global geographic metadata with no regions."}, "multiRegionalMetadata": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyMultiRegional", "description": "Multi-regional geographic metadata with 2 or more regions."}, "regionalMetadata": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegional", "description": "Regional geographic metadata with 1 region."}, "type": {"description": "Type of geographic taxonomy associated with the billing account SKU group SKU.", "enum": ["TYPE_UNSPECIFIED", "TYPE_GLOBAL", "TYPE_REGIONAL", "TYPE_MULTI_REGIONAL"], "enumDescriptions": ["Default value. Unspecified type.", "Global geographic taxonomy with no regions.", "Regional geographic taxonomy with 1 region.", "Multi-regional geographic taxonomy with 2 or more regions."], "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyGlobal": {"description": "Encapsulates a global geographic taxonomy.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyGlobal", "properties": {}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyMultiRegional": {"description": "Encapsulates a multi-regional geographic taxonomy.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyMultiRegional", "properties": {"regions": {"description": "Google Cloud regions associated with the multi-regional geographic taxonomy.", "items": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegion"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegion": {"description": "Encapsulates a Google Cloud region.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegion", "properties": {"region": {"description": "Description of a Google Cloud region. Example: \"us-west2\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegional": {"description": "Encapsulates a regional geographic taxonomy.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegional", "properties": {"region": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaGeoTaxonomyRegion", "description": "Google Cloud region associated with the regional geographic taxonomy."}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaListBillingAccountSkuGroupSkusResponse": {"description": "Response message for ListBillingAccountSkuGroupSkus.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaListBillingAccountSkuGroupSkusResponse", "properties": {"billingAccountSkuGroupSkus": {"description": "The returned billing account SKU group SKUs.", "items": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaBillingAccountSkuGroupSku"}, "type": "array"}, "nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaProductTaxonomy": {"description": "Encapsulates product categories, such as `Serverless`, `Cloud Run`, `TaskQueue`, and others.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaProductTaxonomy", "properties": {"taxonomyCategories": {"description": "All product categories that the billing account SKU group SKU belong to.", "items": {"$ref": "GoogleCloudBillingBillingaccountskugroupskusV1betaTaxonomyCategory"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskugroupskusV1betaTaxonomyCategory": {"description": "Encapsulates a product category.", "id": "GoogleCloudBillingBillingaccountskugroupskusV1betaTaxonomyCategory", "properties": {"category": {"description": "Name of the product category.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaBillingAccountSku": {"description": "Encapsulates a stock keeping unit (SKU) visible to a billing account. A SKU distinctly identifies a resource that you can purchase. For a list of available SKUs, see [SKUs](https://cloud.google.com/skus).", "id": "GoogleCloudBillingBillingaccountskusV1betaBillingAccountSku", "properties": {"billingAccountService": {"description": "BillingAccountService that the BillingAccountSku belongs to.", "type": "string"}, "displayName": {"description": "Description of the BillingAccountSku. Example: \"A2 Instance Core running in Hong Kong\".", "type": "string"}, "geoTaxonomy": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomy", "description": "Geographic metadata that applies to the BillingAccountSku."}, "name": {"description": "Resource name for the BillingAccountSku. Example: \"billingAccounts/012345-567890-ABCDEF/skus/AA95-CD31-42FE\".", "type": "string"}, "productTaxonomy": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaProductTaxonomy", "description": "List of product categories that apply to the BillingAccountSku."}, "skuId": {"description": "Unique identifier for the SKU. It is the string after the collection identifier \"skus/\" Example: \"AA95-CD31-42FE\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomy": {"description": "Encapsulates geographic metadata, such as regions and multi-regions like `us-east4` or `European Union`.", "id": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomy", "properties": {"globalMetadata": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyGlobal", "description": "Global geographic metadata with no regions."}, "multiRegionalMetadata": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyMultiRegional", "description": "Multi-regional geographic metadata with 2 or more regions."}, "regionalMetadata": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegional", "description": "Regional geographic metadata with 1 region."}, "type": {"description": "Type of geographic taxonomy associated with the billing account SKU.", "enum": ["TYPE_UNSPECIFIED", "TYPE_GLOBAL", "TYPE_REGIONAL", "TYPE_MULTI_REGIONAL"], "enumDescriptions": ["Default value. Unspecified type.", "Global geographic taxonomy with no regions.", "Regional geographic taxonomy with 1 region.", "Multi-regional geographic taxonomy with 2 or more regions."], "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyGlobal": {"description": "Encapsulates a global geographic taxonomy.", "id": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyGlobal", "properties": {}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyMultiRegional": {"description": "Encapsulates a multi-regional geographic taxonomy.", "id": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyMultiRegional", "properties": {"regions": {"description": "Google Cloud regions associated with the multi-regional geographic taxonomy.", "items": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegion"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegion": {"description": "Encapsulates a Google Cloud region.", "id": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegion", "properties": {"region": {"description": "Description of a Google Cloud region. Example: \"us-west2\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegional": {"description": "Encapsulates a regional geographic taxonomy.", "id": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegional", "properties": {"region": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaGeoTaxonomyRegion", "description": "Google Cloud region associated with the regional geographic taxonomy."}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaListBillingAccountSkusResponse": {"description": "Response message for ListBillingAccountSkus.", "id": "GoogleCloudBillingBillingaccountskusV1betaListBillingAccountSkusResponse", "properties": {"billingAccountSkus": {"description": "The returned billing account SKUs.", "items": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaBillingAccountSku"}, "type": "array"}, "nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaProductTaxonomy": {"description": "Encapsulates product categories, such as `Serverless`, `Cloud Run`, `TaskQueue`, and others.", "id": "GoogleCloudBillingBillingaccountskusV1betaProductTaxonomy", "properties": {"taxonomyCategories": {"description": "All product categories that the billing account SKU belong to.", "items": {"$ref": "GoogleCloudBillingBillingaccountskusV1betaTaxonomyCategory"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingBillingaccountskusV1betaTaxonomyCategory": {"description": "Encapsulates a product category.", "id": "GoogleCloudBillingBillingaccountskusV1betaTaxonomyCategory", "properties": {"category": {"description": "Name of the product category.", "type": "string"}}, "type": "object"}, "GoogleCloudBillingPricesV1betaAggregationInfo": {"description": "Encapsulates the aggregation information such as aggregation level and interval for a price.", "id": "GoogleCloudBillingPricesV1betaAggregationInfo", "properties": {"interval": {"description": "Interval at which usage is aggregated to compute cost. Example: \"MONTHLY\" interval indicates that usage is aggregated every month.", "enum": ["INTERVAL_UNSPECIFIED", "INTERVAL_MONTHLY", "INTERVAL_DAILY"], "enumDescriptions": ["Default unspecified value.", "Usage is aggregated every month.", "Usage is aggregated every day."], "type": "string"}, "level": {"description": "Level at which usage is aggregated to compute cost. Example: \"ACCOUNT\" level indicates that usage is aggregated across all projects in a single account.", "enum": ["LEVEL_UNSPECIFIED", "LEVEL_ACCOUNT", "LEVEL_PROJECT"], "enumDescriptions": ["Default unspecified value.", "Usage is aggregated at an account level.", "Usage is aggregated at a project level."], "type": "string"}}, "type": "object"}, "GoogleCloudBillingPricesV1betaListPricesResponse": {"description": "Response message for ListPrices.", "id": "GoogleCloudBillingPricesV1betaListPricesResponse", "properties": {"nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}, "prices": {"description": "The returned publicly listed prices.", "items": {"$ref": "GoogleCloudBillingPricesV1betaPrice"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingPricesV1betaPrice": {"description": "Encapsulates the latest price for a SKU.", "id": "GoogleCloudBillingPricesV1betaPrice", "properties": {"currencyCode": {"description": "ISO-4217 currency code for the price.", "type": "string"}, "name": {"description": "Resource name for the latest price.", "type": "string"}, "rate": {"$ref": "GoogleCloudBillingPricesV1betaRate", "description": "Rate price metadata. SKUs with `Rate` price are offered by pricing tiers. The price can have 1 or more rate pricing tiers."}, "valueType": {"description": "Type of the price. It can have values: [\"unspecified\", \"rate\"].", "type": "string"}}, "type": "object"}, "GoogleCloudBillingPricesV1betaRate": {"description": "Encapsulates a `Rate` price. SKUs with `Rate` price are offered by pricing tiers. The price have 1 or more rate pricing tiers.", "id": "GoogleCloudBillingPricesV1betaRate", "properties": {"aggregationInfo": {"$ref": "GoogleCloudBillingPricesV1betaAggregationInfo", "description": "Aggregation info for tiers such as aggregation level and interval."}, "tiers": {"description": "All tiers associated with the `Rate` price.", "items": {"$ref": "GoogleCloudBillingPricesV1betaRateTier"}, "type": "array"}, "unitInfo": {"$ref": "GoogleCloudBillingPricesV1betaUnitInfo", "description": "Unit info such as name and quantity."}}, "type": "object"}, "GoogleCloudBillingPricesV1betaRateTier": {"description": "Encapsulates a rate price tier.", "id": "GoogleCloudBillingPricesV1betaRateTier", "properties": {"listPrice": {"$ref": "Money", "description": "List price of one tier."}, "startAmount": {"$ref": "Decimal", "description": "Lower bound amount for a tier. Tiers 0-100, 100-200 will be represented with two tiers with `start_amount` 0 and 100."}}, "type": "object"}, "GoogleCloudBillingPricesV1betaUnitInfo": {"description": "Encapsulates the unit information for a Rate", "id": "GoogleCloudBillingPricesV1betaUnitInfo", "properties": {"unit": {"description": "Shorthand for the unit. Example: GiBy.mo.", "type": "string"}, "unitDescription": {"description": "Human-readable description of the unit. Example: gibibyte month.", "type": "string"}, "unitQuantity": {"$ref": "Decimal", "description": "Unit quantity for the tier. Example: if the RateTier price is $1 per 1000000 Bytes, then `unit_quantity` is set to 1000000."}}, "type": "object"}, "GoogleCloudBillingSkugroupsV1betaListSkuGroupsResponse": {"description": "Response message for ListSkuGroups.", "id": "GoogleCloudBillingSkugroupsV1betaListSkuGroupsResponse", "properties": {"nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}, "skuGroups": {"description": "The returned publicly listed SKU groups.", "items": {"$ref": "GoogleCloudBillingSkugroupsV1betaSkuGroup"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingSkugroupsV1betaSkuGroup": {"description": "Encapsulates a publicly listed stock keeping unit (SKU) group. A SKU group represents a collection of SKUs that are related to each other. For example, the `AI Platform APIs` SKU group includes SKUs from the Cloud Dialogflow API, the Cloud Text-to-Speech API, and additional related APIs.", "id": "GoogleCloudBillingSkugroupsV1betaSkuGroup", "properties": {"displayName": {"description": "Description of the SKU group. Example: \"A2 VMs (1 Year CUD)\".", "type": "string"}, "name": {"description": "Resource name for the SKU group. Example: \"skuGroups/0e6403d1-4694-44d2-a696-7a78b1a69301\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomy": {"description": "Encapsulates geographic metadata, such as regions and multi-regions like `us-east4` or `European Union`.", "id": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomy", "properties": {"globalMetadata": {"$ref": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyGlobal", "description": "Global geographic metadata with no regions."}, "multiRegionalMetadata": {"$ref": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyMultiRegional", "description": "Multi-regional geographic metadata with 2 or more regions."}, "regionalMetadata": {"$ref": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegional", "description": "Regional geographic metadata with 1 region."}, "type": {"description": "Type of geographic taxonomy associated with the SKU group SKU.", "enum": ["TYPE_UNSPECIFIED", "TYPE_GLOBAL", "TYPE_REGIONAL", "TYPE_MULTI_REGIONAL"], "enumDescriptions": ["Default value. Unspecified type.", "Global geographic taxonomy with no regions.", "Regional geographic taxonomy with 1 region.", "Multi-regional geographic taxonomy with 2 or more regions."], "type": "string"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyGlobal": {"description": "Encapsulates a global geographic taxonomy.", "id": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyGlobal", "properties": {}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyMultiRegional": {"description": "Encapsulates a multi-regional geographic taxonomy.", "id": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyMultiRegional", "properties": {"regions": {"description": "Google Cloud regions associated with the multi-regional geographic taxonomy.", "items": {"$ref": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegion"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegion": {"description": "Encapsulates a Google Cloud region.", "id": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegion", "properties": {"region": {"description": "Description of a Google Cloud region. Example: \"us-west2\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegional": {"description": "Encapsulates a regional geographic taxonomy.", "id": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegional", "properties": {"region": {"$ref": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomyRegion", "description": "Google Cloud region associated with the regional geographic taxonomy."}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaListSkuGroupSkusResponse": {"description": "Response message for ListSkuGroupSkus.", "id": "GoogleCloudBillingSkugroupskusV1betaListSkuGroupSkusResponse", "properties": {"nextPageToken": {"description": "Token that can be sent as `page_token` in the subsequent request to retrieve the next page. If this field is empty, there are no subsequent pages.", "type": "string"}, "skuGroupSkus": {"description": "The returned SKU group SKUs.", "items": {"$ref": "GoogleCloudBillingSkugroupskusV1betaSkuGroupSku"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaProductTaxonomy": {"description": "Encapsulates product categories, such as `Serverless`, `Cloud Run`, `TaskQueue`, and others.", "id": "GoogleCloudBillingSkugroupskusV1betaProductTaxonomy", "properties": {"taxonomyCategories": {"description": "All product categories that the SKU group SKU belongs to.", "items": {"$ref": "GoogleCloudBillingSkugroupskusV1betaTaxonomyCategory"}, "type": "array"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaSkuGroupSku": {"description": "Encapsulates a publicly listed stock keeping unit (SKU) that is part of a publicly listed SKU group. A SKU group represents a collection of SKUs that are related to each other. For example, the `AI Platform APIs` SKU group includes SKUs from the Cloud Dialogflow API, the Cloud Text-to-Speech API, and additional related APIs.", "id": "GoogleCloudBillingSkugroupskusV1betaSkuGroupSku", "properties": {"displayName": {"description": "Description of the SkuGroupSku. Example: \"A2 Instance Core running in Hong Kong\".", "type": "string"}, "geoTaxonomy": {"$ref": "GoogleCloudBillingSkugroupskusV1betaGeoTaxonomy", "description": "Geographic metadata that applies to the SkuGroupSku."}, "name": {"description": "Resource name for the SkuGroupSku. Example: \"skuGroups/0e6403d1-4694-44d2-a696-7a78b1a69301/skus/AA95-CD31-42FE\".", "type": "string"}, "productTaxonomy": {"$ref": "GoogleCloudBillingSkugroupskusV1betaProductTaxonomy", "description": "List of product categories that apply to the SkuGroupSku."}, "service": {"description": "Service that the SkuGroupSku belongs to.", "type": "string"}, "skuId": {"description": "Unique identifier for the SKU. It is the string after the collection identifier \"skus/\" Example: \"AA95-CD31-42FE\".", "type": "string"}}, "type": "object"}, "GoogleCloudBillingSkugroupskusV1betaTaxonomyCategory": {"description": "Encapsulates a product category.", "id": "GoogleCloudBillingSkugroupskusV1betaTaxonomyCategory", "properties": {"category": {"description": "Name of the product category.", "type": "string"}}, "type": "object"}, "GuestAccelerator": {"description": "Specification of a set of guest accelerators attached to a machine.", "id": "GuestAccelerator", "properties": {"acceleratorCount": {"description": "The number of the guest accelerator cards exposed to each instance.", "format": "int64", "type": "string"}, "acceleratorType": {"description": "The type of the guest accelerator cards. For example: \"nvidia-tesla-t4\".", "type": "string"}}, "type": "object"}, "InterRegionEgress": {"description": "Data transfer between two regions.", "id": "InterRegionEgress", "properties": {"destinationRegion": {"description": "Which [region](https://cloud.google.com/compute/docs/regions-zones) the data is transferred to.", "type": "string"}, "egressRate": {"$ref": "Usage", "description": "VM to VM data transfer usage. The expected units such are GiBy/s, By/s, and so on."}, "sourceRegion": {"description": "Which [region](https://cloud.google.com/compute/docs/regions-zones) the data is transferred from.", "type": "string"}}, "type": "object"}, "IntraRegionEgress": {"description": "Data transfer within the same region. When the source region and destination region are in the same zone, using internal IP addresses, there isn't any charge for data transfer.", "id": "IntraRegionEgress", "properties": {"egressRate": {"$ref": "Usage", "description": "VM to VM data transfer usage. The expected are GiBy/s, By/s, and so on."}}, "type": "object"}, "MachineType": {"description": "Specification of machine series, memory, and number of vCPUs.", "id": "MachineType", "properties": {"customMachineType": {"$ref": "CustomMachineType"}, "predefinedMachineType": {"$ref": "PredefinedMachineType"}}, "type": "object"}, "Money": {"description": "Represents an amount of money with its currency type.", "id": "Money", "properties": {"currencyCode": {"description": "The three-letter currency code defined in ISO 4217.", "type": "string"}, "nanos": {"description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32", "type": "integer"}, "units": {"description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar.", "format": "int64", "type": "string"}}, "type": "object"}, "MultiRegional": {"description": "Area contains multiple locations.", "id": "MultiRegional", "properties": {"name": {"description": "The [location name](https://cloud.google.com/storage/docs/locations#available-locations) where the data is stored. For example: \"us\" for multi-region.", "type": "string"}}, "type": "object"}, "PersistentDisk": {"description": "Specification of a persistent disk attached to a VM.", "id": "PersistentDisk", "properties": {"diskSize": {"$ref": "Usage", "description": "Specifies the size of disk. Must be at least 10 GB."}, "diskType": {"description": "The [disk type](https://cloud.google.com/compute/docs/disks#disk-types). For example: \"pd-standard\".", "type": "string"}, "provisionedIops": {"$ref": "Usage", "description": "Indicates how many IOPS to provision for the disk for extreme persistent disks. This sets the number of I/O operations per second that the disk can handle. Values must be between 10,000 and 120,000."}, "scope": {"description": "The geographic scope of the disk. Defaults to `SCOPE_ZONAL` if not specified.", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_ZONAL", "SCOPE_REGIONAL"], "enumDescriptions": ["Unspecified.", "The disk exists in a single zone.", "The disk is replicated in a secondary zone within the same region."], "type": "string"}}, "type": "object"}, "PredefinedMachineType": {"description": "Specification of a predefined machine type.", "id": "PredefinedMachineType", "properties": {"machineType": {"description": "The [machine type](https://cloud.google.com/compute/docs/machine-types). For example: \"n1-standard1\".", "type": "string"}}, "type": "object"}, "PremiumTierEgressWorkload": {"description": "Specify a Premium Tier Internet Data Transfer networking workload.", "id": "PremiumTierEgressWorkload", "properties": {"destinationContinent": {"description": "Where the data is sent to.", "enum": ["DESTINATION_CONTINENT_UNSPECIFIED", "DESTINATION_CONTINENT_ASIA_PACIFIC", "DESTINATION_CONTINENT_AFRICA", "DESTINATION_CONTINENT_NORTH_AMERICA", "DESTINATION_CONTINENT_AUTRALIA", "DESTINATION_CONTINENT_CENTRAL_AMERICA", "DESTINATION_CONTINENT_CHINA", "DESTINATION_CONTINENT_EASTERN_EUROPE", "DESTINATION_CONTINENT_WESTERN_EUROPE", "DESTINATION_CONTINENT_EMEA", "DESTINATION_CONTINENT_INDIA", "DESTINATION_CONTINENT_MIDDLE_EAST", "DESTINATION_CONTINENT_SOUTH_AMERICA"], "enumDescriptions": ["Not specified.", "Asia Pacific.", "Africa.", "North America.", "Australia.", "Central America.", "China.", "Eastern Europe.", "Western Europe.", "Other regions in Europe, Middle East and Africa.", "India", "Middle East.", "South America."], "type": "string"}, "egressRate": {"$ref": "Usage", "description": "Premium Tier Data Transfer usage. The expected units are GiBy/s, By/s, and so on, based on [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard."}, "sourceRegion": {"description": "Which [region](https://cloud.google.com/compute/docs/regions-zones) the data comes from.", "type": "string"}}, "type": "object"}, "Price": {"description": "The price of a SKU at a point int time.", "id": "Price", "properties": {"effectiveTime": {"$ref": "EstimationTimePoint", "description": "The timestamp within the estimation time frame when the price was set."}, "priceType": {"description": "The type of price. Possible values: \"RATE\"", "type": "string"}, "rate": {"$ref": "Rate", "description": "A set of tiered rates."}}, "type": "object"}, "Rate": {"description": "A SKU price consisting of tiered rates.", "id": "Rate", "properties": {"tiers": {"description": "The service tiers.", "items": {"$ref": "RateTier"}, "type": "array"}, "unit": {"description": "The SKU's pricing unit. For example, if the tier price is $1 per 1000000 Bytes, then this field will show 'By'. The `start_amount` field in each tier will be in this unit.", "type": "string"}, "unitCount": {"description": "The SKU's count for the pricing unit. For example, if the tier price is $1 per 1000000 Bytes, then this column will show 1000000.", "format": "double", "type": "number"}}, "type": "object"}, "RateTier": {"description": "Pricing details for a service tier.", "id": "RateTier", "properties": {"price": {"$ref": "Money", "description": "The price for this tier."}, "startAmount": {"description": "The magnitude of usage in which the tier interval begins. Example: \"From 100 GiBi the price is $1 per byte\" implies `start_amount` = 100", "format": "double", "type": "number"}}, "type": "object"}, "Regional": {"description": "Area contains only one location.", "id": "Regional", "properties": {"name": {"description": "The [location name](https://cloud.google.com/storage/docs/locations#available-locations). For example: \"us-central1\" for region.", "type": "string"}}, "type": "object"}, "ScenarioConfig": {"description": "Configuration for a CostScenario. Specifies how costs are calculated.", "id": "ScenarioConfig", "properties": {"estimateDuration": {"description": "Time frame for the estimate. Workloads must specify usage for this duration. Duration must be at least 1 hour (3,600 seconds) and at most 10 years (315,360,000 seconds). The calculations for years and months are based on a 730-hour (2,628,000-second) month. For durations longer than one month (2,628,000 seconds), the duration is rounded up to the next month, so the estimate shows you the costs for full months. For example, a duration of 3,232,800 seconds (roughly 5 weeks) is rounded up to 2 months.", "format": "google-duration", "type": "string"}}, "type": "object"}, "SegmentCostEstimate": {"description": "Workload cost estimates for a single time segment.", "id": "SegmentCostEstimate", "properties": {"commitmentCostEstimates": {"description": "Estimated costs for each commitment.", "items": {"$ref": "CommitmentCostEstimate"}, "type": "array"}, "segmentStartTime": {"$ref": "EstimationTimePoint", "description": "Timestamp for the start of the segment."}, "segmentTotalCostEstimate": {"$ref": "CostEstimate", "description": "Total estimated costs for the time segment."}, "workloadCostEstimates": {"description": "Estimated costs for each workload.", "items": {"$ref": "WorkloadCostEstimate"}, "type": "array"}}, "type": "object"}, "Sku": {"description": "Information about SKUs appearing in the cost estimate.", "id": "S<PERSON>", "properties": {"displayName": {"description": "The display name for the SKU. Example: A2 Instance Core running in Americas", "type": "string"}, "prices": {"description": "A timeline of prices for a SKU in chronological order. Note: The API currently only supports using a constant price for the entire estimation time frame so this list will contain a single value.", "items": {"$ref": "Price"}, "type": "array"}, "sku": {"description": "The resource name for the SKU. Example: \"services/DA34-426B-A397/skus/AA95-CD31-42FE\"", "type": "string"}}, "type": "object"}, "SkuCostEstimate": {"description": "Estimated cost for usage on a SKU.", "id": "SkuCostEstimate", "properties": {"costEstimate": {"$ref": "CostEstimate", "description": "The estimated cost for the usage on this SKU."}, "sku": {"description": "The resource name for the SKU. Example: \"services/DA34-426B-A397/skus/AA95-CD31-42FE\" More information about the SKU can be found in the `skus` field of the `CostEstimationResult`.", "type": "string"}, "usageAmount": {"description": "The amount of usage on this SKU.", "format": "double", "type": "number"}, "usageUnit": {"description": "The unit for the usage on this SKU.", "type": "string"}}, "type": "object"}, "StandardTierEgressWorkload": {"description": "Specify Standard Tier Internet Data Transfer.", "id": "StandardTierEgressWorkload", "properties": {"egressRate": {"$ref": "Usage", "description": "Standard Tier Data Transfer usage. The expected units are GiBy/s, By/s, and so on, based on the [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard."}, "sourceRegion": {"description": "Which [region](https://cloud.google.com/compute/docs/regions-zones) the data is transferred from.", "type": "string"}}, "type": "object"}, "Usage": {"description": "An amount of usage over a time frame.", "id": "Usage", "properties": {"usageRateTimeline": {"$ref": "UsageRateTimeline", "description": "A timeline of usage rates over the estimate interval."}}, "type": "object"}, "UsageRateTimeline": {"description": "A timeline of usage rates. Consists of a series of entries, each of which specifies a constant rate of usage during a time interval. Each entry contains an effective time. The usage rate is in effect from that time until the effective time of the subsequent entry, or, for the last entry, for the remaining portion of estimation time frame. Effective times are specified as an offset into the estimation time frame. Usage is considered to be zero until the `effective_time` of the first entry. All subsequent entries must have an effective time greater than the previous entry and less than the estimate time frame. The effective time on all entries must be an integer number of hours.", "id": "UsageRateTimeline", "properties": {"unit": {"description": "The unit for the usage rate in each timeline entry. If you provide an incorrect unit for an instance, the correct unit is provided in the error message. The supported units are a subset of [The Unified Code for Units of Measure](https://ucum.org/ucum.html) standard: * **Time units (TIME-UNIT)** * `s` second * `min` minute * `h` hour * `d` day * `wk` week * `mo` month * `yr` year * `ms` millisecond * `us` microsecond * `ns` nanosecond * **Basic storage units (BASIC-STORAGE-UNIT)** * `bit` bit * `By` byte * **Count units (COUNT-UNIT)** * `count` count * **Prefixes (PREFIX)** * `k` kilo (10^3) * `M` mega (10^6) * `G` giga (10^9) * `T` tera (10^12) * `P` peta (10^15) * `Ki` kibi (2^10) * `Mi` mebi (2^20) * `Gi` gibi (2^30) * `Ti` tebi (2^40) * `Pi` pebi (2^50) **Grammar** The grammar also includes these connectors: * `/` division or ratio (as an infix operator). For example: `kBy/{email}` or `MiBy/10ms`. * `.` multiplication or composition (as an infix operator). For example: `GBy.d` or `k{watt}.h`. The grammar for a unit is as follows: ``` Expression = Component { \".\" Component } { \"/\" Component } ; Component = ( [ PREFIX ] UNIT | \"%\" ) [ Annotation ] | Annotation | \"1\" ; UNIT = TIME-UNIT | STORAGE-UNIT | DATA-UNIT | COUNT-UNIT Annotation = \"{\" NAME \"}\" ; ``` Examples: * Request per second: `1/s` or `{requests}/s` * GibiBytes: `GiBy` * GibiBytes * seconds: `GiBy.s`", "type": "string"}, "usageRateTimelineEntries": {"description": "The timeline entries. Each entry has a start time and usage rate. The start time specifies the effective time of the usage rate. The entries must be sorted by start time in an increasing order.", "items": {"$ref": "UsageRateTimelineEntry"}, "type": "array"}}, "type": "object"}, "UsageRateTimelineEntry": {"description": "A usage rate timeline entry. Each entry specifies a constant usage rate during a time interval.", "id": "UsageRateTimelineEntry", "properties": {"effectiveTime": {"$ref": "EstimationTimePoint", "description": "The effective time for this entry. The usage rate is in effect starting at this time until the effective time of the subsequent entry in the timeline. The last entry defines the usage rate until the end of the `Usage` time frame. Must correspond to an integer number of hours."}, "usageRate": {"description": "The usage rate.", "format": "double", "type": "number"}}, "type": "object"}, "VlanAttachment": {"description": "VLAN attachment for Cloud Interconnect.", "id": "VlanAttachment", "properties": {"bandwidth": {"description": "Capacities in the [pricing table](https://cloud.google.com/vpc/network-pricing#interconnect-pricing) Examples of capacity are: 50/100/200/300/400/500-Mbps, 1/2/5/10/20/50-Gbps.", "enum": ["BANDWIDTH_UNSPECIFIED", "BANDWIDTH_BPS_50M", "BANDWIDTH_BPS_100M", "BANDWIDTH_BPS_200M", "BANDWIDTH_BPS_300M", "BANDWIDTH_BPS_400M", "BANDWIDTH_BPS_500M", "BANDWIDTH_BPS_1G", "BANDWIDTH_BPS_2G", "BANDWIDTH_BPS_5G", "BANDWIDTH_BPS_10G", "BANDWIDTH_BPS_20G", "BANDWIDTH_BPS_50G"], "enumDescriptions": ["Unspecified", "50 Mbit/s", "100 Mbit/s", "200 Mbit/s", "300 Mbit/s", "400 Mbit/s", "500 Mbit/s", "1 Gbit/s", "2 Gbit/s", "5 Gbit/s", "10 Gbit/s", "20 Gbit/s", "50 Gbit/s"], "type": "string"}, "vlanCount": {"$ref": "Usage", "description": "VLAN usage. This is specified as a unitless quantity which indicates the number of VLAN attachment used in interconnect."}}, "type": "object"}, "VmResourceBasedCud": {"description": "Specifies a resource-based committed use discount (CUD).", "id": "VmResourceBasedCud", "properties": {"guestAccelerator": {"$ref": "GuestAccelerator", "description": "Guest accelerator, known as GPU."}, "machineSeries": {"description": "The machine series for CUD. For example: \"n1\" for general purpose N1 machine type commitments. \"n2\" for general purpose N2 machine type commitments. \"e2\" for general purpose E2 machine type commitments. \"n2d\" for general purpose N2D machine type commitments. \"t2d\" for general purpose T2D machine type commitments. \"c2\"/\"c2d\" for compute-optimized commitments. \"m1\"/\"m2\" for the memory-optimized commitments. \"a2' for the accelerator-optimized commitments.", "type": "string"}, "memorySizeGb": {"description": "Memory size of the VM in GB (2^30 bytes). Must be an increment of 0.25 (256 MB).", "format": "double", "type": "number"}, "plan": {"description": "Commitment usage plan.", "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "TWELVE_MONTH", "THIRTY_SIX_MONTH"], "enumDescriptions": ["Not specified commitment plan.", "1 year commitment.", "3 years commitment."], "type": "string"}, "region": {"description": "The region where the VM runs. For example: \"us-central1\"", "type": "string"}, "virtualCpuCount": {"description": "The number of vCPUs. The number of vCPUs must be an integer of 0 or more and can be even or odd.", "format": "int64", "type": "string"}}, "type": "object"}, "VmToVmEgressWorkload": {"description": "Specify VM to VM data transfer.", "id": "VmToVmEgressWorkload", "properties": {"interRegionEgress": {"$ref": "InterRegionEgress"}, "intraRegionEgress": {"$ref": "IntraRegionEgress"}}, "type": "object"}, "Workload": {"description": "Specifies usage on a single Google Cloud product over a time frame. Each Google Cloud product has its own message, containing specific product configuration parameters of the product usage amounts along each dimension in which the product is billed.", "id": "Workload", "properties": {"cloudCdnEgressWorkload": {"$ref": "CloudCdnEgressWorkload", "description": "Usage on Google Cloud CDN Data Transfer."}, "cloudCdnWorkload": {"$ref": "CloudCdnWorkload", "description": "Usage on Google Cloud CDN."}, "cloudInterconnectEgressWorkload": {"$ref": "CloudInterconnectEgressWorkload", "description": "Usage on Google Cloud Interconnect Data Transfer."}, "cloudInterconnectWorkload": {"$ref": "CloudInterconnectWorkload", "description": "Usage on Google Cloud Interconnect."}, "cloudStorageEgressWorkload": {"$ref": "CloudStorageEgressWorkload", "description": "Usage on Cloud Storage Data Transfer."}, "cloudStorageWorkload": {"$ref": "CloudStorageWorkload", "description": "Usage on Google Cloud Storage."}, "computeVmWorkload": {"$ref": "ComputeVmWorkload", "description": "Usage of a Google Compute Engine Virtual Machine."}, "name": {"description": "Required. A name for this workload. All workloads in a `CostScenario` must have a unique `name`. Each `name` may be at most 128 characters long.", "type": "string"}, "premiumTierEgressWorkload": {"$ref": "PremiumTierEgressWorkload", "description": "Usage on Premium Tier Internet Data Transfer."}, "standardTierEgressWorkload": {"$ref": "StandardTierEgressWorkload", "description": "Usage on Standard Tier Internet Data Transfer."}, "vmToVmEgressWorkload": {"$ref": "VmToVmEgressWorkload", "description": "Usage on VM to VM Data Transfer."}}, "type": "object"}, "WorkloadCostEstimate": {"description": "Estimated cost for a workload.", "id": "WorkloadCostEstimate", "properties": {"name": {"description": "The name of the workload, as specified in the `CostScenario`.", "type": "string"}, "skuCostEstimates": {"description": "Estimated costs for each SKU in the workload.", "items": {"$ref": "SkuCostEstimate"}, "type": "array"}, "workloadTotalCostEstimate": {"$ref": "CostEstimate", "description": "Total estimated costs for the workload."}}, "type": "object"}}, "servicePath": "", "title": "Cloud Billing API", "version": "v1beta", "version_module": true}