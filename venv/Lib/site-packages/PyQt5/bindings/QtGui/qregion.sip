// qregion.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRegion
{
%TypeHeaderCode
#include <qregion.h>
%End

public:
    enum RegionType
    {
        Rectangle,
        Ellipse,
    };

    QRegion();
    QRegion(int x, int y, int w, int h, QRegion::RegionType type = QRegion::Rectangle);
    QRegion(const QRect &r, QRegion::RegionType type = QRegion::Rectangle);
    QRegion(const QPolygon &a, Qt::FillRule fillRule = Qt::OddEvenFill);
    QRegion(const QBitmap &bitmap);
    QRegion(const QRegion &region);
    QRegion(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QRegion>())
            sipCpp = new QRegion(a0->value<QRegion>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QRegion();
    bool isEmpty() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isEmpty();
%End

    bool contains(const QPoint &p) const;
    int __contains__(const QPoint &p) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    bool contains(const QRect &r) const;
    int __contains__(const QRect &r) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    void translate(int dx, int dy);
    void translate(const QPoint &p);
    QRegion translated(int dx, int dy) const;
    QRegion translated(const QPoint &p) const;
    QRegion united(const QRegion &r) const;
    QRegion united(const QRect &r) const;
    QRect boundingRect() const;
    QVector<QRect> rects() const;
%If (Qt_5_4_0 -)
    QRegion operator|(const QRegion &r) const;
%End
    void setRects(const QVector<QRect> &);
%MethodCode
        if (a0->size())
            sipCpp->setRects(a0->data(), a0->size());
        else
            sipCpp->setRects(0, 0);
%End

%If (- Qt_5_4_0)
    const QRegion operator|(const QRegion &r) const;
%End
%If (Qt_5_4_0 -)
    QRegion operator+(const QRegion &r) const;
%End
%If (- Qt_5_4_0)
    const QRegion operator+(const QRegion &r) const;
%End
%If (Qt_5_4_0 -)
    QRegion operator+(const QRect &r) const;
%End
%If (- Qt_5_4_0)
    const QRegion operator+(const QRect &r) const;
%End
%If (Qt_5_4_0 -)
    QRegion operator&(const QRegion &r) const;
%End
%If (- Qt_5_4_0)
    const QRegion operator&(const QRegion &r) const;
%End
%If (Qt_5_4_0 -)
    QRegion operator&(const QRect &r) const;
%End
%If (- Qt_5_4_0)
    const QRegion operator&(const QRect &r) const;
%End
%If (Qt_5_4_0 -)
    QRegion operator-(const QRegion &r) const;
%End
%If (- Qt_5_4_0)
    const QRegion operator-(const QRegion &r) const;
%End
%If (Qt_5_4_0 -)
    QRegion operator^(const QRegion &r) const;
%End
%If (- Qt_5_4_0)
    const QRegion operator^(const QRegion &r) const;
%End
    QRegion &operator|=(const QRegion &r);
    QRegion &operator+=(const QRegion &r);
    QRegion &operator+=(const QRect &r);
    QRegion &operator&=(const QRegion &r);
    QRegion &operator&=(const QRect &r);
    QRegion &operator-=(const QRegion &r);
    QRegion &operator^=(const QRegion &r);
    bool operator==(const QRegion &r) const;
    bool operator!=(const QRegion &r) const;
    QRegion intersected(const QRegion &r) const;
    QRegion intersected(const QRect &r) const;
    QRegion subtracted(const QRegion &r) const;
    QRegion xored(const QRegion &r) const;
    bool intersects(const QRegion &r) const;
    bool intersects(const QRect &r) const;
    int rectCount() const;
    void swap(QRegion &other /Constrained/);
    bool isNull() const;
};

QDataStream &operator<<(QDataStream &, const QRegion & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QRegion & /Constrained/) /ReleaseGIL/;
