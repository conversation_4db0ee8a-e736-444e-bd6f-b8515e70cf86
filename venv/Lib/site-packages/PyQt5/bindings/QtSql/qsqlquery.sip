// qsqlquery.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlQuery
{
%TypeHeaderCode
#include <qsqlquery.h>
%End

public:
    enum BatchExecutionMode
    {
        ValuesAsRows,
        ValuesAsColumns,
    };

    explicit QSqlQuery(QSqlResult *r);
    QSqlQuery(const QString &query = QString(), QSqlDatabase db = QSqlDatabase()) /ReleaseGIL/;
    explicit QSqlQuery(QSqlDatabase db);
    QSqlQuery(const QSqlQuery &other);
    ~QSqlQuery();
    bool isValid() const;
    bool isActive() const;
    bool isNull(int field) const;
%If (Qt_5_3_0 -)
    bool isNull(const QString &name) const;
%End
    int at() const;
    QString lastQuery() const;
    int numRowsAffected() const;
    QSqlError lastError() const;
    bool isSelect() const;
    int size() const;
    const QSqlDriver *driver() const;
    const QSqlResult *result() const;
    bool isForwardOnly() const;
    QSqlRecord record() const;
    void setForwardOnly(bool forward);
    bool exec(const QString &query) /PyName=exec_,ReleaseGIL/;
%If (Py_v3)
    bool exec(const QString &query) /ReleaseGIL/;
%End
    QVariant value(int i) const;
    QVariant value(const QString &name) const;
    bool seek(int index, bool relative = false) /ReleaseGIL/;
    bool next() /ReleaseGIL/;
    bool previous() /ReleaseGIL/;
    bool first() /ReleaseGIL/;
    bool last() /ReleaseGIL/;
    void clear() /ReleaseGIL/;
    bool exec() /PyName=exec_,ReleaseGIL/;
%If (Py_v3)
    bool exec() /ReleaseGIL/;
%End
    bool execBatch(QSqlQuery::BatchExecutionMode mode = QSqlQuery::ValuesAsRows);
    bool prepare(const QString &query) /ReleaseGIL/;
    void bindValue(const QString &placeholder, const QVariant &val, QSql::ParamType type = QSql::In);
    void bindValue(int pos, const QVariant &val, QSql::ParamType type = QSql::In);
    void addBindValue(const QVariant &val, QSql::ParamType type = QSql::In);
    QVariant boundValue(const QString &placeholder) const;
    QVariant boundValue(int pos) const;
    QMap<QString, QVariant> boundValues() const;
    QString executedQuery() const;
    QVariant lastInsertId() const;
    void setNumericalPrecisionPolicy(QSql::NumericalPrecisionPolicy precisionPolicy);
    QSql::NumericalPrecisionPolicy numericalPrecisionPolicy() const;
    void finish();
    bool nextResult();
};
