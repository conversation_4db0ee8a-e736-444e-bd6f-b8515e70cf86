import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Controls 2.15'

Module {
    dependencies: [
        "QtQuick 2.11",
        "QtQuick.Templates 2.5",
        "QtQuick.Window 2.2"
    ]
    Component {
        name: "QQuickCheckLabel"
        defaultProperty: "data"
        prototype: "QQuickText"
        exports: ["QtQuick.Controls.impl/CheckLabel 2.3"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickClippedText"
        defaultProperty: "data"
        prototype: "QQuickText"
        exports: ["QtQuick.Controls.impl/ClippedText 2.2"]
        exportMetaObjectRevisions: [0]
        Property { name: "clipX"; type: "double" }
        Property { name: "clipY"; type: "double" }
        Property { name: "clipWidth"; type: "double" }
        Property { name: "clipHeight"; type: "double" }
    }
    Component {
        name: "QQuickColor"
        prototype: "QObject"
        exports: ["QtQuick.Controls.impl/Color 2.3"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Method {
            name: "transparent"
            type: "QColor"
            Parameter { name: "color"; type: "QColor" }
            Parameter { name: "opacity"; type: "double" }
        }
        Method {
            name: "blend"
            type: "QColor"
            Parameter { name: "a"; type: "QColor" }
            Parameter { name: "b"; type: "QColor" }
            Parameter { name: "factor"; type: "double" }
        }
    }
    Component {
        name: "QQuickColorImage"
        defaultProperty: "data"
        prototype: "QQuickImage"
        exports: ["QtQuick.Controls.impl/ColorImage 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "color"; type: "QColor" }
        Property { name: "defaultColor"; type: "QColor" }
    }
    Component {
        name: "QQuickDefaultBusyIndicator"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.impl/BusyIndicatorImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "pen"; type: "QColor" }
        Property { name: "fill"; type: "QColor" }
        Property { name: "running"; type: "bool" }
    }
    Component {
        name: "QQuickDefaultDial"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["QtQuick.Controls.impl/DialImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "progress"; type: "double" }
        Property { name: "color"; type: "QColor" }
    }
    Component {
        name: "QQuickDefaultProgressBar"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.impl/ProgressBarImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "indeterminate"; type: "bool" }
        Property { name: "progress"; type: "double" }
        Property { name: "color"; type: "QColor" }
    }
    Component {
        name: "QQuickDefaultStyle"
        prototype: "QObject"
        exports: ["QtQuick.Controls.impl/Default 2.1"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Property { name: "backgroundColor"; type: "QColor"; isReadonly: true }
        Property { name: "overlayModalColor"; type: "QColor"; isReadonly: true }
        Property { name: "overlayDimColor"; type: "QColor"; isReadonly: true }
        Property { name: "textColor"; type: "QColor"; isReadonly: true }
        Property { name: "textDarkColor"; type: "QColor"; isReadonly: true }
        Property { name: "textLightColor"; type: "QColor"; isReadonly: true }
        Property { name: "textLinkColor"; type: "QColor"; isReadonly: true }
        Property { name: "textSelectionColor"; type: "QColor"; isReadonly: true }
        Property { name: "textDisabledColor"; type: "QColor"; isReadonly: true }
        Property { name: "textDisabledLightColor"; type: "QColor"; isReadonly: true }
        Property { name: "textPlaceholderColor"; type: "QColor"; isReadonly: true }
        Property { name: "focusColor"; type: "QColor"; isReadonly: true }
        Property { name: "focusLightColor"; type: "QColor"; isReadonly: true }
        Property { name: "focusPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonCheckedColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonCheckedPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonCheckedFocusColor"; type: "QColor"; isReadonly: true }
        Property { name: "toolButtonColor"; type: "QColor"; isReadonly: true }
        Property { name: "tabButtonColor"; type: "QColor"; isReadonly: true }
        Property { name: "tabButtonPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "tabButtonCheckedPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "delegateColor"; type: "QColor"; isReadonly: true }
        Property { name: "delegatePressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "delegateFocusColor"; type: "QColor"; isReadonly: true }
        Property { name: "indicatorPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "indicatorDisabledColor"; type: "QColor"; isReadonly: true }
        Property { name: "indicatorFrameColor"; type: "QColor"; isReadonly: true }
        Property { name: "indicatorFramePressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "indicatorFrameDisabledColor"; type: "QColor"; isReadonly: true }
        Property { name: "frameDarkColor"; type: "QColor"; isReadonly: true }
        Property { name: "frameLightColor"; type: "QColor"; isReadonly: true }
        Property { name: "scrollBarColor"; type: "QColor"; isReadonly: true }
        Property { name: "scrollBarPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "progressBarColor"; type: "QColor"; isReadonly: true }
        Property { name: "pageIndicatorColor"; type: "QColor"; isReadonly: true }
        Property { name: "separatorColor"; type: "QColor"; isReadonly: true }
        Property { name: "disabledDarkColor"; type: "QColor"; isReadonly: true }
        Property { name: "disabledLightColor"; type: "QColor"; isReadonly: true }
    }
    Component {
        name: "QQuickIconImage"
        defaultProperty: "data"
        prototype: "QQuickImage"
        exports: ["QtQuick.Controls.impl/IconImage 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Property { name: "color"; type: "QColor" }
    }
    Component {
        name: "QQuickIconLabel"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.impl/IconLabel 2.3"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Display"
            values: {
                "IconOnly": 0,
                "TextOnly": 1,
                "TextBesideIcon": 2,
                "TextUnderIcon": 3
            }
        }
        Property { name: "icon"; type: "QQuickIcon" }
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Property { name: "color"; type: "QColor" }
        Property { name: "display"; type: "Display" }
        Property { name: "spacing"; type: "double" }
        Property { name: "mirrored"; type: "bool" }
        Property { name: "alignment"; type: "Qt::Alignment" }
        Property { name: "topPadding"; type: "double" }
        Property { name: "leftPadding"; type: "double" }
        Property { name: "rightPadding"; type: "double" }
        Property { name: "bottomPadding"; type: "double" }
    }
    Component {
        name: "QQuickImplicitSizeItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Property { name: "implicitWidth"; type: "double"; isReadonly: true }
        Property { name: "implicitHeight"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickItemGroup"
        defaultProperty: "data"
        prototype: "QQuickImplicitSizeItem"
        exports: ["QtQuick.Controls.impl/ItemGroup 2.2"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickMnemonicLabel"
        defaultProperty: "data"
        prototype: "QQuickText"
        exports: ["QtQuick.Controls.impl/MnemonicLabel 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "text"; type: "string" }
        Property { name: "mnemonicVisible"; type: "bool" }
    }
    Component {
        name: "QQuickOverlay"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls/Overlay 2.3"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickOverlayAttached"
        Property { name: "modal"; type: "QQmlComponent"; isPointer: true }
        Property { name: "modeless"; type: "QQmlComponent"; isPointer: true }
        Signal { name: "pressed" }
        Signal { name: "released" }
    }
    Component {
        name: "QQuickPaddedRectangle"
        defaultProperty: "data"
        prototype: "QQuickRectangle"
        exports: ["QtQuick.Controls.impl/PaddedRectangle 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "padding"; type: "double" }
        Property { name: "topPadding"; type: "double" }
        Property { name: "leftPadding"; type: "double" }
        Property { name: "rightPadding"; type: "double" }
        Property { name: "bottomPadding"; type: "double" }
    }
    Component {
        name: "QQuickPlaceholderText"
        defaultProperty: "data"
        prototype: "QQuickText"
        exports: ["QtQuick.Controls.impl/PlaceholderText 2.2"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickSplitHandleAttached"
        prototype: "QObject"
        exports: ["QtQuick.Controls/SplitHandle 2.13"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "hovered"; type: "bool"; isReadonly: true }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
    }
    Component {
        name: "QQuickText"
        defaultProperty: "data"
        prototype: "QQuickImplicitSizeItem"
        Enum {
            name: "HAlignment"
            values: {
                "AlignLeft": 1,
                "AlignRight": 2,
                "AlignHCenter": 4,
                "AlignJustify": 8
            }
        }
        Enum {
            name: "VAlignment"
            values: {
                "AlignTop": 32,
                "AlignBottom": 64,
                "AlignVCenter": 128
            }
        }
        Enum {
            name: "TextStyle"
            values: {
                "Normal": 0,
                "Outline": 1,
                "Raised": 2,
                "Sunken": 3
            }
        }
        Enum {
            name: "TextFormat"
            values: {
                "PlainText": 0,
                "RichText": 1,
                "MarkdownText": 3,
                "AutoText": 2,
                "StyledText": 4
            }
        }
        Enum {
            name: "TextElideMode"
            values: {
                "ElideLeft": 0,
                "ElideRight": 1,
                "ElideMiddle": 2,
                "ElideNone": 3
            }
        }
        Enum {
            name: "WrapMode"
            values: {
                "NoWrap": 0,
                "WordWrap": 1,
                "WrapAnywhere": 3,
                "WrapAtWordBoundaryOrAnywhere": 4,
                "Wrap": 4
            }
        }
        Enum {
            name: "RenderType"
            values: {
                "QtRendering": 0,
                "NativeRendering": 1
            }
        }
        Enum {
            name: "LineHeightMode"
            values: {
                "ProportionalHeight": 0,
                "FixedHeight": 1
            }
        }
        Enum {
            name: "FontSizeMode"
            values: {
                "FixedSize": 0,
                "HorizontalFit": 1,
                "VerticalFit": 2,
                "Fit": 3
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Property { name: "color"; type: "QColor" }
        Property { name: "linkColor"; type: "QColor" }
        Property { name: "style"; type: "TextStyle" }
        Property { name: "styleColor"; type: "QColor" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "lineCount"; type: "int"; isReadonly: true }
        Property { name: "truncated"; type: "bool"; isReadonly: true }
        Property { name: "maximumLineCount"; type: "int" }
        Property { name: "textFormat"; type: "TextFormat" }
        Property { name: "elide"; type: "TextElideMode" }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "lineHeight"; type: "double" }
        Property { name: "lineHeightMode"; type: "LineHeightMode" }
        Property { name: "baseUrl"; type: "QUrl" }
        Property { name: "minimumPixelSize"; type: "int" }
        Property { name: "minimumPointSize"; type: "int" }
        Property { name: "fontSizeMode"; type: "FontSizeMode" }
        Property { name: "renderType"; type: "RenderType" }
        Property { name: "hoveredLink"; revision: 2; type: "string"; isReadonly: true }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Property { name: "fontInfo"; revision: 9; type: "QJSValue"; isReadonly: true }
        Property { name: "advance"; revision: 10; type: "QSizeF"; isReadonly: true }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "linkHovered"
            revision: 2
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "styleChanged"
            Parameter { name: "style"; type: "QQuickText::TextStyle" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::VAlignment" }
        }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickText::TextFormat" }
        }
        Signal {
            name: "elideModeChanged"
            Parameter { name: "mode"; type: "QQuickText::TextElideMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "contentWidthChanged"
            Parameter { name: "contentWidth"; type: "double" }
        }
        Signal {
            name: "contentHeightChanged"
            Parameter { name: "contentHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightChanged"
            Parameter { name: "lineHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightModeChanged"
            Parameter { name: "mode"; type: "LineHeightMode" }
        }
        Signal {
            name: "lineLaidOut"
            Parameter { name: "line"; type: "QQuickTextLine"; isPointer: true }
        }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Signal { name: "fontInfoChanged"; revision: 9 }
        Method { name: "doLayout" }
        Method { name: "forceLayout"; revision: 9 }
        Method {
            name: "linkAt"
            revision: 3
            type: "string"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickTumblerView"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.impl/TumblerView 2.1"]
        exportMetaObjectRevisions: [0]
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "path"; type: "QQuickPath"; isPointer: true }
    }
    Component {
        prototype: "QQuickAbstractButton"
        name: "QtQuick.Controls/AbstractButton 2.0"
        exports: ["QtQuick.Controls/AbstractButton 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickAction"
        name: "QtQuick.Controls/Action 2.3"
        exports: ["QtQuick.Controls/Action 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
    }
    Component {
        prototype: "QQuickActionGroup"
        name: "QtQuick.Controls/ActionGroup 2.3"
        exports: ["QtQuick.Controls/ActionGroup 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "actions"
    }
    Component {
        prototype: "QQuickApplicationWindow"
        name: "QtQuick.Controls/ApplicationWindow 2.0"
        exports: ["QtQuick.Controls/ApplicationWindow 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickBusyIndicator"
        name: "QtQuick.Controls/BusyIndicator 2.0"
        exports: ["QtQuick.Controls/BusyIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickButton"
        name: "QtQuick.Controls/Button 2.0"
        exports: ["QtQuick.Controls/Button 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickButtonGroup"
        name: "QtQuick.Controls/ButtonGroup 2.0"
        exports: ["QtQuick.Controls/ButtonGroup 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
    }
    Component {
        prototype: "QQuickCheckBox"
        name: "QtQuick.Controls/CheckBox 2.0"
        exports: ["QtQuick.Controls/CheckBox 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickCheckDelegate"
        name: "QtQuick.Controls/CheckDelegate 2.0"
        exports: ["QtQuick.Controls/CheckDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickComboBox"
        name: "QtQuick.Controls/ComboBox 2.0"
        exports: ["QtQuick.Controls/ComboBox 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickContainer"
        name: "QtQuick.Controls/Container 2.0"
        exports: ["QtQuick.Controls/Container 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickControl"
        name: "QtQuick.Controls/Control 2.0"
        exports: ["QtQuick.Controls/Control 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickDelayButton"
        name: "QtQuick.Controls/DelayButton 2.2"
        exports: ["QtQuick.Controls/DelayButton 2.2"]
        exportMetaObjectRevisions: [2]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickDial"
        name: "QtQuick.Controls/Dial 2.0"
        exports: ["QtQuick.Controls/Dial 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickDialog"
        name: "QtQuick.Controls/Dialog 2.1"
        exports: ["QtQuick.Controls/Dialog 2.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickDialogButtonBox"
        name: "QtQuick.Controls/DialogButtonBox 2.1"
        exports: ["QtQuick.Controls/DialogButtonBox 2.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickDrawer"
        name: "QtQuick.Controls/Drawer 2.0"
        exports: ["QtQuick.Controls/Drawer 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickFrame"
        name: "QtQuick.Controls/Frame 2.0"
        exports: ["QtQuick.Controls/Frame 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickGroupBox"
        name: "QtQuick.Controls/GroupBox 2.0"
        exports: ["QtQuick.Controls/GroupBox 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickHorizontalHeaderView"
        name: "QtQuick.Controls/HorizontalHeaderView 2.15"
        exports: ["QtQuick.Controls/HorizontalHeaderView 2.15"]
        exportMetaObjectRevisions: [15]
        isComposite: true
        defaultProperty: "flickableData"
    }
    Component {
        prototype: "QQuickItemDelegate"
        name: "QtQuick.Controls/ItemDelegate 2.0"
        exports: ["QtQuick.Controls/ItemDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickLabel"
        name: "QtQuick.Controls/Label 2.0"
        exports: ["QtQuick.Controls/Label 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickMenu"
        name: "QtQuick.Controls/Menu 2.0"
        exports: ["QtQuick.Controls/Menu 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickMenuBar"
        name: "QtQuick.Controls/MenuBar 2.3"
        exports: ["QtQuick.Controls/MenuBar 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickMenuBarItem"
        name: "QtQuick.Controls/MenuBarItem 2.3"
        exports: ["QtQuick.Controls/MenuBarItem 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickMenuItem"
        name: "QtQuick.Controls/MenuItem 2.0"
        exports: ["QtQuick.Controls/MenuItem 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickMenuSeparator"
        name: "QtQuick.Controls/MenuSeparator 2.1"
        exports: ["QtQuick.Controls/MenuSeparator 2.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickPage"
        name: "QtQuick.Controls/Page 2.0"
        exports: ["QtQuick.Controls/Page 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickPageIndicator"
        name: "QtQuick.Controls/PageIndicator 2.0"
        exports: ["QtQuick.Controls/PageIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickPane"
        name: "QtQuick.Controls/Pane 2.0"
        exports: ["QtQuick.Controls/Pane 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickPopup"
        name: "QtQuick.Controls/Popup 2.0"
        exports: ["QtQuick.Controls/Popup 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickProgressBar"
        name: "QtQuick.Controls/ProgressBar 2.0"
        exports: ["QtQuick.Controls/ProgressBar 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickRadioButton"
        name: "QtQuick.Controls/RadioButton 2.0"
        exports: ["QtQuick.Controls/RadioButton 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickRadioDelegate"
        name: "QtQuick.Controls/RadioDelegate 2.0"
        exports: ["QtQuick.Controls/RadioDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickRangeSlider"
        name: "QtQuick.Controls/RangeSlider 2.0"
        exports: ["QtQuick.Controls/RangeSlider 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickRoundButton"
        name: "QtQuick.Controls/RoundButton 2.1"
        exports: ["QtQuick.Controls/RoundButton 2.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickScrollBar"
        name: "QtQuick.Controls/ScrollBar 2.0"
        exports: ["QtQuick.Controls/ScrollBar 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickScrollIndicator"
        name: "QtQuick.Controls/ScrollIndicator 2.0"
        exports: ["QtQuick.Controls/ScrollIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickScrollView"
        name: "QtQuick.Controls/ScrollView 2.2"
        exports: ["QtQuick.Controls/ScrollView 2.2"]
        exportMetaObjectRevisions: [2]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickSlider"
        name: "QtQuick.Controls/Slider 2.0"
        exports: ["QtQuick.Controls/Slider 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickSpinBox"
        name: "QtQuick.Controls/SpinBox 2.0"
        exports: ["QtQuick.Controls/SpinBox 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickSplitView"
        name: "QtQuick.Controls/SplitView 2.13"
        exports: ["QtQuick.Controls/SplitView 2.13"]
        exportMetaObjectRevisions: [13]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickStackView"
        name: "QtQuick.Controls/StackView 2.0"
        exports: ["QtQuick.Controls/StackView 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickSwipeDelegate"
        name: "QtQuick.Controls/SwipeDelegate 2.0"
        exports: ["QtQuick.Controls/SwipeDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickSwipeView"
        name: "QtQuick.Controls/SwipeView 2.0"
        exports: ["QtQuick.Controls/SwipeView 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickSwitch"
        name: "QtQuick.Controls/Switch 2.0"
        exports: ["QtQuick.Controls/Switch 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickSwitchDelegate"
        name: "QtQuick.Controls/SwitchDelegate 2.0"
        exports: ["QtQuick.Controls/SwitchDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickTabBar"
        name: "QtQuick.Controls/TabBar 2.0"
        exports: ["QtQuick.Controls/TabBar 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickTabButton"
        name: "QtQuick.Controls/TabButton 2.0"
        exports: ["QtQuick.Controls/TabButton 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickTextArea"
        name: "QtQuick.Controls/TextArea 2.0"
        exports: ["QtQuick.Controls/TextArea 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickTextField"
        name: "QtQuick.Controls/TextField 2.0"
        exports: ["QtQuick.Controls/TextField 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickToolBar"
        name: "QtQuick.Controls/ToolBar 2.0"
        exports: ["QtQuick.Controls/ToolBar 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickToolButton"
        name: "QtQuick.Controls/ToolButton 2.0"
        exports: ["QtQuick.Controls/ToolButton 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickToolSeparator"
        name: "QtQuick.Controls/ToolSeparator 2.1"
        exports: ["QtQuick.Controls/ToolSeparator 2.1"]
        exportMetaObjectRevisions: [1]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickToolTip"
        name: "QtQuick.Controls/ToolTip 2.0"
        exports: ["QtQuick.Controls/ToolTip 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "contentData"
    }
    Component {
        prototype: "QQuickTumbler"
        name: "QtQuick.Controls/Tumbler 2.0"
        exports: ["QtQuick.Controls/Tumbler 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickVerticalHeaderView"
        name: "QtQuick.Controls/VerticalHeaderView 2.15"
        exports: ["QtQuick.Controls/VerticalHeaderView 2.15"]
        exportMetaObjectRevisions: [15]
        isComposite: true
        defaultProperty: "flickableData"
    }
}
