"""
Agent Tester Module

Handles testing of AI agents with different LLM providers
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from .models.agent import Agent
from .models.llm_config import LLMConfig
from .models.secret import Secret
from .llm_providers import LLMProviderManager


class AgentTestResult:
    """Result of an agent test"""
    
    def __init__(self, success: bool, response: str = "", error: str = "", metadata: Dict[str, Any] = None):
        self.success = success
        self.response = response
        self.error = error
        self.metadata = metadata or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "success": self.success,
            "response": self.response,
            "error": self.error,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }


class AgentTester:
    """Tester for AI agents"""
    
    def __init__(self):
        self.provider_manager = LLMProviderManager()
        self.test_history: List[Dict[str, Any]] = []
    
    def register_llm_config(self, config: LLMConfig, secret: Optional[Secret] = None):
        """Register an LLM configuration"""
        return self.provider_manager.register_provider(config, secret)
    
    def add_secret(self, secret: Secret):
        """Add a secret for use with providers"""
        self.provider_manager.add_secret(secret)
    
    async def test_agent(self, agent: Agent, user_input: str, llm_config: Optional[LLMConfig] = None) -> AgentTestResult:
        """Test an agent with user input"""
        try:
            # Use provided LLM config or agent's default
            if llm_config is None:
                llm_config = LLMConfig(
                    id=f"default_{agent.id}",
                    provider=agent.default_llm_provider,
                    model_name=agent.default_llm_model
                )
            
            # Register provider if not already registered
            provider = self.provider_manager.get_provider(llm_config.id)
            if not provider:
                secret = None
                if agent.secret_name:
                    secret = self.provider_manager.get_secret(agent.secret_name)
                
                provider = self.provider_manager.register_provider(llm_config, secret)
                if not provider:
                    return AgentTestResult(
                        success=False,
                        error=f"Failed to create provider for {llm_config.provider}"
                    )
            
            # Build the complete prompt
            complete_prompt = self._build_prompt(agent, user_input)
            
            # Generate response
            result = await provider.generate_response(complete_prompt)
            
            if result.get("success"):
                test_result = AgentTestResult(
                    success=True,
                    response=result.get("response", ""),
                    metadata={
                        "agent_id": agent.id,
                        "agent_name": agent.name,
                        "llm_provider": llm_config.provider,
                        "llm_model": llm_config.model_name,
                        "user_input": user_input,
                        "prompt_length": len(complete_prompt),
                        "response_metadata": result.get("metadata", {})
                    }
                )
            else:
                test_result = AgentTestResult(
                    success=False,
                    error=result.get("error", "Unknown error"),
                    metadata={
                        "agent_id": agent.id,
                        "agent_name": agent.name,
                        "llm_provider": llm_config.provider,
                        "llm_model": llm_config.model_name,
                        "user_input": user_input
                    }
                )
            
            # Add to test history
            self.test_history.append(test_result.to_dict())
            
            return test_result
            
        except Exception as e:
            return AgentTestResult(
                success=False,
                error=f"Test failed: {str(e)}",
                metadata={
                    "agent_id": agent.id if agent else "unknown",
                    "user_input": user_input
                }
            )
    
    def _build_prompt(self, agent: Agent, user_input: str) -> str:
        """Build complete prompt for the agent"""
        prompt_parts = []
        
        # System prompt
        if agent.system_prompt:
            prompt_parts.append(f"System: {agent.system_prompt}")
        
        # Agent characteristics
        characteristics = []
        if agent.tone:
            characteristics.append(f"Tone: {agent.tone}")
        if agent.capabilities:
            characteristics.append(f"Capabilities: {', '.join(agent.capabilities)}")
        if agent.tools:
            characteristics.append(f"Available tools: {', '.join(agent.tools)}")
        
        if characteristics:
            prompt_parts.append(f"Agent characteristics: {'; '.join(characteristics)}")
        
        # User prompt template (if any)
        if agent.user_prompt:
            prompt_parts.append(f"Instructions: {agent.user_prompt}")
        
        # Actual user input
        prompt_parts.append(f"User: {user_input}")
        
        # Add assistant prompt
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    async def test_agent_capabilities(self, agent: Agent, llm_config: Optional[LLMConfig] = None) -> List[AgentTestResult]:
        """Test agent with predefined capability tests"""
        test_prompts = [
            "Hello, please introduce yourself and explain what you can do.",
            "What are your main capabilities and strengths?",
            "How do you approach problem-solving?",
            "Can you help me with creative tasks?",
            "What tools do you have access to?",
            "How do you handle complex analytical tasks?",
            "Explain your communication style and tone.",
            "What makes you unique as an AI assistant?"
        ]
        
        results = []
        for prompt in test_prompts:
            result = await self.test_agent(agent, prompt, llm_config)
            results.append(result)
            
            # Small delay between tests to avoid rate limiting
            await asyncio.sleep(1)
        
        return results
    
    async def benchmark_agent(self, agent: Agent, test_inputs: List[str], llm_configs: List[LLMConfig]) -> Dict[str, List[AgentTestResult]]:
        """Benchmark agent across multiple LLM configurations"""
        benchmark_results = {}
        
        for llm_config in llm_configs:
            config_key = f"{llm_config.provider}_{llm_config.model_name}"
            benchmark_results[config_key] = []
            
            for test_input in test_inputs:
                result = await self.test_agent(agent, test_input, llm_config)
                benchmark_results[config_key].append(result)
                
                # Small delay between tests
                await asyncio.sleep(1)
        
        return benchmark_results
    
    def get_test_history(self, agent_id: Optional[str] = None, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get test history, optionally filtered by agent ID"""
        history = self.test_history
        
        if agent_id:
            history = [test for test in history if test.get("metadata", {}).get("agent_id") == agent_id]
        
        if limit:
            history = history[-limit:]
        
        return history
    
    def get_test_statistics(self, agent_id: Optional[str] = None) -> Dict[str, Any]:
        """Get test statistics"""
        history = self.get_test_history(agent_id)
        
        if not history:
            return {"total_tests": 0}
        
        successful_tests = [test for test in history if test.get("success")]
        failed_tests = [test for test in history if not test.get("success")]
        
        # Provider statistics
        provider_stats = {}
        for test in history:
            provider = test.get("metadata", {}).get("llm_provider", "unknown")
            if provider not in provider_stats:
                provider_stats[provider] = {"total": 0, "successful": 0, "failed": 0}
            
            provider_stats[provider]["total"] += 1
            if test.get("success"):
                provider_stats[provider]["successful"] += 1
            else:
                provider_stats[provider]["failed"] += 1
        
        # Response time statistics (if available)
        response_times = []
        for test in successful_tests:
            metadata = test.get("metadata", {}).get("response_metadata", {})
            if "total_duration" in metadata:
                response_times.append(metadata["total_duration"])
        
        stats = {
            "total_tests": len(history),
            "successful_tests": len(successful_tests),
            "failed_tests": len(failed_tests),
            "success_rate": len(successful_tests) / len(history) if history else 0,
            "provider_stats": provider_stats
        }
        
        if response_times:
            stats["avg_response_time"] = sum(response_times) / len(response_times)
            stats["min_response_time"] = min(response_times)
            stats["max_response_time"] = max(response_times)
        
        return stats
    
    def clear_test_history(self, agent_id: Optional[str] = None):
        """Clear test history, optionally for a specific agent"""
        if agent_id:
            self.test_history = [test for test in self.test_history 
                               if test.get("metadata", {}).get("agent_id") != agent_id]
        else:
            self.test_history.clear()
    
    def export_test_history(self, file_path: str, agent_id: Optional[str] = None) -> bool:
        """Export test history to a file"""
        try:
            import json
            history = self.get_test_history(agent_id)
            
            with open(file_path, 'w') as f:
                json.dump(history, f, indent=2, default=str)
            
            return True
        except Exception as e:
            print(f"Error exporting test history: {e}")
            return False
