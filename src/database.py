"""
Database module for persistent storage using SQLite
"""

import sqlite3
import json
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from contextlib import contextmanager

try:
    # Try relative imports first (when used as module)
    from .models.agent import Agent
    from .models.llm_config import LLMConfig
    from .models.secret import Secret, APIKey
    from .models.project import Project, ProjectFile
except ImportError:
    # Fall back to absolute imports (when used in tests)
    from models.agent import Agent
    from models.llm_config import LLMConfig
    from models.secret import Secret, APIKey
    from models.project import Project, ProjectFile


class Database:
    """SQLite database manager for Hyper Collaborative Agents"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # Store database in user's home directory
            home_dir = os.path.expanduser("~")
            self.db_path = os.path.join(home_dir, ".hyper_collaborative_agents.db")
        else:
            self.db_path = db_path
        
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """Initialize database tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Agents table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS agents (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    system_prompt TEXT NOT NULL,
                    user_prompt TEXT,
                    max_tokens INTEGER DEFAULT 1000,
                    tools TEXT,  -- JSON array
                    capabilities TEXT,  -- JSON array
                    tone TEXT DEFAULT 'professional',
                    paraphrase_randomization REAL DEFAULT 0.0,
                    llm_config_name TEXT,
                    secret_name TEXT,
                    project_ids TEXT,  -- JSON array of project IDs
                    metadata TEXT,  -- JSON object
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # LLM Configurations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS llm_configs (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    provider TEXT NOT NULL,
                    model_name TEXT NOT NULL,
                    secret_name TEXT,
                    base_url TEXT,
                    port INTEGER,
                    temperature REAL DEFAULT 0.7,
                    max_tokens INTEGER,
                    parameters TEXT,  -- JSON object
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Secrets table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS secrets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    provider TEXT NOT NULL,
                    rotation_strategy TEXT DEFAULT 'round_robin',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # API Keys table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    secret_id INTEGER NOT NULL,
                    name TEXT,
                    key_value TEXT NOT NULL,  -- Encrypted
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_used TIMESTAMP,
                    usage_count INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (secret_id) REFERENCES secrets (id) ON DELETE CASCADE
                )
            ''')

            # Projects table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    knowledge_base_path TEXT,
                    metadata TEXT,  -- JSON object
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Project Files table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS project_files (
                    id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_type TEXT,
                    file_size INTEGER,
                    content_hash TEXT,
                    processed BOOLEAN DEFAULT 0,
                    metadata TEXT,  -- JSON object
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
                )
            ''')
            
            conn.commit()
    
    # Agent operations
    def save_agent(self, agent: Agent) -> bool:
        """Save or update an agent"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO agents (
                        id, name, description, system_prompt, user_prompt, max_tokens,
                        tools, capabilities, tone, paraphrase_randomization,
                        llm_config_name, secret_name, project_ids, metadata, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    agent.id, agent.name, agent.description, agent.system_prompt,
                    agent.user_prompt, agent.max_tokens,
                    json.dumps(agent.tools), json.dumps(agent.capabilities),
                    agent.tone, agent.paraphrase_randomization,
                    getattr(agent, 'llm_config_name', None), agent.secret_name,
                    json.dumps(getattr(agent, 'project_ids', [])),
                    json.dumps(agent.metadata), datetime.now().isoformat()
                ))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"Error saving agent: {e}")
            return False
    
    def load_agents(self) -> List[Agent]:
        """Load all agents from database"""
        agents = []
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM agents ORDER BY name')
                
                for row in cursor.fetchall():
                    agent = Agent(
                        id=row['id'],
                        name=row['name'],
                        description=row['description'] or "",
                        system_prompt=row['system_prompt'],
                        user_prompt=row['user_prompt'],
                        max_tokens=row['max_tokens'],
                        tools=json.loads(row['tools'] or '[]'),
                        capabilities=json.loads(row['capabilities'] or '[]'),
                        tone=row['tone'],
                        paraphrase_randomization=row['paraphrase_randomization'],
                        default_llm_provider="",  # Will be set from config
                        default_llm_model="",     # Will be set from config
                        secret_name=row['secret_name'],
                        metadata=json.loads(row['metadata'] or '{}')
                    )
                    # Add custom attributes
                    agent.llm_config_name = row['llm_config_name']
                    agent.project_ids = json.loads(row['project_ids'] or '[]')
                    agents.append(agent)
        except Exception as e:
            print(f"Error loading agents: {e}")
        
        return agents
    
    def delete_agent(self, agent_id: str) -> bool:
        """Delete an agent"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM agents WHERE id = ?', (agent_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"Error deleting agent: {e}")
            return False
    
    # LLM Config operations
    def save_llm_config(self, config: LLMConfig, name: str) -> bool:
        """Save or update an LLM configuration"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO llm_configs (
                        id, name, provider, model_name, secret_name, base_url, port,
                        temperature, max_tokens, parameters, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    config.id, name, config.provider, config.model_name,
                    config.secret_name, config.base_url, config.port,
                    config.temperature, config.max_tokens,
                    json.dumps(config.parameters), datetime.now().isoformat()
                ))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"Error saving LLM config: {e}")
            return False
    
    def load_llm_configs(self) -> List[Dict[str, Any]]:
        """Load all LLM configurations"""
        configs = []
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM llm_configs ORDER BY name')
                
                for row in cursor.fetchall():
                    config_data = {
                        'id': row['id'],
                        'name': row['name'],
                        'provider': row['provider'],
                        'model_name': row['model_name'],
                        'secret_name': row['secret_name'],
                        'base_url': row['base_url'],
                        'port': row['port'],
                        'temperature': row['temperature'],
                        'max_tokens': row['max_tokens'],
                        'parameters': json.loads(row['parameters'] or '{}'),
                        'created_at': row['created_at'],
                        'updated_at': row['updated_at']
                    }
                    configs.append(config_data)
        except Exception as e:
            print(f"Error loading LLM configs: {e}")
        
        return configs
    
    def delete_llm_config(self, config_id: str) -> bool:
        """Delete an LLM configuration"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM llm_configs WHERE id = ?', (config_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"Error deleting LLM config: {e}")
            return False
    
    # Secret operations
    def save_secret(self, secret: Secret) -> bool:
        """Save or update a secret with its API keys"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Save or update secret
                cursor.execute('''
                    INSERT OR REPLACE INTO secrets (
                        name, provider, rotation_strategy, updated_at
                    ) VALUES (?, ?, ?, ?)
                ''', (
                    secret.name, secret.provider, secret.rotation_strategy,
                    datetime.now().isoformat()
                ))
                
                # Get secret ID
                cursor.execute('SELECT id FROM secrets WHERE name = ?', (secret.name,))
                secret_id = cursor.fetchone()['id']
                
                # Delete existing API keys for this secret
                cursor.execute('DELETE FROM api_keys WHERE secret_id = ?', (secret_id,))
                
                # Insert API keys
                for api_key in secret.api_keys:
                    cursor.execute('''
                        INSERT INTO api_keys (
                            secret_id, name, key_value, created_at, last_used,
                            usage_count, is_active
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        secret_id, api_key.name, api_key.key,
                        api_key.created_at.isoformat(),
                        api_key.last_used.isoformat() if api_key.last_used else None,
                        api_key.usage_count, api_key.is_active
                    ))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"Error saving secret: {e}")
            return False
    
    def load_secrets(self) -> List[Secret]:
        """Load all secrets with their API keys"""
        secrets = []
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Load secrets
                cursor.execute('SELECT * FROM secrets ORDER BY name')
                for secret_row in cursor.fetchall():
                    # Load API keys for this secret
                    cursor.execute('''
                        SELECT * FROM api_keys WHERE secret_id = ? ORDER BY created_at
                    ''', (secret_row['id'],))
                    
                    api_keys = []
                    for key_row in cursor.fetchall():
                        api_key = APIKey(
                            key=key_row['key_value'],
                            name=key_row['name'],
                            created_at=datetime.fromisoformat(key_row['created_at']),
                            last_used=datetime.fromisoformat(key_row['last_used']) if key_row['last_used'] else None,
                            usage_count=key_row['usage_count'],
                            is_active=bool(key_row['is_active'])
                        )
                        api_keys.append(api_key)
                    
                    secret = Secret(
                        name=secret_row['name'],
                        provider=secret_row['provider'],
                        api_keys=api_keys,
                        rotation_strategy=secret_row['rotation_strategy'],
                        created_at=datetime.fromisoformat(secret_row['created_at']),
                        updated_at=datetime.fromisoformat(secret_row['updated_at'])
                    )
                    secrets.append(secret)
        except Exception as e:
            print(f"Error loading secrets: {e}")
        
        return secrets
    
    def delete_secret(self, secret_name: str) -> bool:
        """Delete a secret and its API keys"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM secrets WHERE name = ?', (secret_name,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"Error deleting secret: {e}")
            return False

    # Project operations
    def save_project(self, project: Project) -> bool:
        """Save or update a project with its files"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Save or update project
                cursor.execute('''
                    INSERT OR REPLACE INTO projects (
                        id, name, description, knowledge_base_path, metadata, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    project.id, project.name, project.description,
                    project.knowledge_base_path, json.dumps(project.metadata),
                    datetime.now().isoformat()
                ))

                # Delete existing files for this project
                cursor.execute('DELETE FROM project_files WHERE project_id = ?', (project.id,))

                # Insert project files
                for file in project.files:
                    cursor.execute('''
                        INSERT INTO project_files (
                            id, project_id, name, file_path, file_type, file_size,
                            content_hash, processed, metadata, uploaded_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        file.id, project.id, file.name, file.file_path,
                        file.file_type, file.file_size, file.content_hash,
                        file.processed, json.dumps(file.metadata),
                        file.uploaded_at.isoformat()
                    ))

                conn.commit()
                return True
        except Exception as e:
            print(f"Error saving project: {e}")
            return False

    def load_projects(self) -> List[Project]:
        """Load all projects with their files"""
        projects = []
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Load projects
                cursor.execute('SELECT * FROM projects ORDER BY name')
                for project_row in cursor.fetchall():
                    # Load files for this project
                    cursor.execute('''
                        SELECT * FROM project_files WHERE project_id = ? ORDER BY uploaded_at
                    ''', (project_row['id'],))

                    files = []
                    for file_row in cursor.fetchall():
                        project_file = ProjectFile(
                            id=file_row['id'],
                            name=file_row['name'],
                            file_path=file_row['file_path'],
                            file_type=file_row['file_type'],
                            file_size=file_row['file_size'],
                            content_hash=file_row['content_hash'],
                            processed=bool(file_row['processed']),
                            metadata=json.loads(file_row['metadata'] or '{}'),
                            uploaded_at=datetime.fromisoformat(file_row['uploaded_at'])
                        )
                        files.append(project_file)

                    project = Project(
                        id=project_row['id'],
                        name=project_row['name'],
                        description=project_row['description'] or "",
                        files=files,
                        knowledge_base_path=project_row['knowledge_base_path'] or "",
                        metadata=json.loads(project_row['metadata'] or '{}'),
                        created_at=datetime.fromisoformat(project_row['created_at']),
                        updated_at=datetime.fromisoformat(project_row['updated_at'])
                    )
                    projects.append(project)
        except Exception as e:
            print(f"Error loading projects: {e}")

        return projects

    def delete_project(self, project_id: str) -> bool:
        """Delete a project and its files"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM projects WHERE id = ?', (project_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"Error deleting project: {e}")
            return False
