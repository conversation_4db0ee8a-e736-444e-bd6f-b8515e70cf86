import sys
from PyQt5.QtWidgets import (QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout,
                            QWidget, QMenuBar, QStatusBar, QMessageBox, QAction,
                            QPushButton, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

from .ui.agent_builder_widget import AgentBuilderWidget
from .ui.agent_test_widget import AgentTestWidget
from .ui.projects_widget import ProjectsWidget
from .ui.settings_modal import SettingsModal
from .database import Database
from .secrets_manager import SecretsManager


class MainWindow(QMainWindow):
    """Main application window for Hyper Collaborative Agents"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Hyper Collaborative Agents")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize database and secrets manager
        self.db = Database()
        self.secrets_manager = SecretsManager()

        # Data storage
        self.agents = []
        self.llm_configs = []
        self.secrets = []
        self.projects = []

        # Initialize UI
        self.init_ui()
        self.init_menu()
        self.init_status_bar()
        self.connect_signals()

        # Load data from database
        self.load_data()

        # Show welcome message
        QTimer.singleShot(1000, self.show_welcome)
    
    def init_ui(self):
        """Initialize the main UI components"""
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Create header layout with tabs and settings button
        header_layout = QHBoxLayout()

        # Create tab widget
        self.tab_widget = QTabWidget()
        header_layout.addWidget(self.tab_widget)

        # Settings button (aligned with tab bar)
        self.settings_btn = QPushButton("⚙️ Settings")
        self.settings_btn.setFixedSize(100, 32)  # Match tab height
        self.settings_btn.clicked.connect(self.show_settings)
        header_layout.addWidget(self.settings_btn)

        layout.addLayout(header_layout)

        # Initialize tabs
        self.init_tabs()

        # Initialize settings modal
        self.settings_modal = SettingsModal(self)
    
    def init_tabs(self):
        """Initialize all tabs"""
        # Agent Builder tab
        self.agent_builder = AgentBuilderWidget()
        self.tab_widget.addTab(self.agent_builder, "Agent Builder")

        # Projects tab
        self.projects_widget = ProjectsWidget()
        self.tab_widget.addTab(self.projects_widget, "Projects")

        # Agent Testing tab
        self.agent_test = AgentTestWidget()
        self.tab_widget.addTab(self.agent_test, "Agent Testing")
    
    def init_menu(self):
        """Initialize menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        # New Agent action
        new_agent_action = QAction('New Agent', self)
        new_agent_action.setShortcut('Ctrl+N')
        new_agent_action.triggered.connect(self.new_agent)
        file_menu.addAction(new_agent_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        # About action
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def init_status_bar(self):
        """Initialize status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def new_agent(self):
        """Create a new agent"""
        self.tab_widget.setCurrentIndex(0)  # Switch to Agent Builder tab
        self.agent_builder.new_agent()
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Hyper Collaborative Agents",
                         "Hyper Collaborative Agents v1.0\n\n"
                         "A LangGraph and PyQt application for AI agent collaboration.\n"
                         "Built with Python and LangGraph.")
    
    def load_data(self):
        """Load data from database"""
        # Load secrets using secrets manager (handles encryption)
        secret_names = self.secrets_manager.list_secrets()
        self.secrets = []
        for name in secret_names:
            secret = self.secrets_manager.get_secret(name)
            if secret:
                self.secrets.append(secret)

        # Load LLM configs
        self.llm_configs = self.db.load_llm_configs()

        # Load agents
        self.agents = self.db.load_agents()

        # Load projects
        self.projects = self.db.load_projects()

        # Update UI components
        self.update_all_components()

    def update_all_components(self):
        """Update all UI components with current data"""
        # Update secrets in settings modal
        secret_names = [s.name for s in self.secrets]

        # Update LLM configs in agent builder
        config_names = [config['name'] for config in self.llm_configs]
        self.agent_builder.llm_config_combo.clear()
        self.agent_builder.llm_config_combo.addItems(config_names)

        # Update settings modal
        self.settings_modal.update_data(
            llm_configs=self.llm_configs,
            secrets=self.secrets
        )

        # Update agent builder with existing agents and projects
        self.agent_builder.update_agents_list(self.agents)
        self.agent_builder.update_projects_list(self.projects)

        # Update projects widget
        self.projects_widget.update_projects_data(self.projects)

        # Update agent testing widget
        self.agent_test.update_data(
            agents=self.agents,
            llm_configs=self.llm_configs,
            secrets=self.secrets
        )

    def connect_signals(self):
        """Connect signals between components"""
        # Connect agent creation signal
        self.agent_builder.agent_created.connect(self.on_agent_created)

        # Connect settings modal signals
        self.settings_modal.secret_created.connect(self.on_secret_created)
        self.settings_modal.secret_updated.connect(self.on_secret_updated)
        self.settings_modal.config_created.connect(self.on_llm_config_created)

        # Connect project signals
        self.projects_widget.project_created.connect(self.on_project_created)
        self.projects_widget.project_updated.connect(self.on_project_updated)
        self.projects_widget.project_deleted.connect(self.on_project_deleted)

    def on_agent_created(self, agent):
        """Handle new agent creation"""
        # Save to database
        if self.db.save_agent(agent):
            # Reload agents from database
            self.agents = self.db.load_agents()

            # Update agent builder and testing widget
            self.agent_builder.update_agents_list(self.agents)
            self.agent_test.update_data(agents=self.agents)

            self.status_bar.showMessage(f"Agent '{agent.name}' saved successfully!", 3000)
        else:
            self.status_bar.showMessage(f"Failed to save agent '{agent.name}'!", 3000)

    def on_secret_created(self, secret):
        """Handle new secret creation"""
        # Save using secrets manager (handles encryption)
        if self.secrets_manager.add_secret(secret):
            # Reload secrets
            secret_names = self.secrets_manager.list_secrets()
            self.secrets = []
            for name in secret_names:
                loaded_secret = self.secrets_manager.get_secret(name)
                if loaded_secret:
                    self.secrets.append(loaded_secret)

            # Update all components
            self.update_all_components()

            self.status_bar.showMessage(f"Secret '{secret.name}' saved successfully!", 3000)
        else:
            self.status_bar.showMessage(f"Failed to save secret '{secret.name}'!", 3000)

    def on_secret_updated(self, secret):
        """Handle secret updates"""
        # Save using secrets manager (handles encryption)
        if self.secrets_manager.update_secret(secret):
            # Reload secrets
            secret_names = self.secrets_manager.list_secrets()
            self.secrets = []
            for name in secret_names:
                loaded_secret = self.secrets_manager.get_secret(name)
                if loaded_secret:
                    self.secrets.append(loaded_secret)

            # Update all components
            self.update_all_components()

            self.status_bar.showMessage(f"Secret '{secret.name}' updated successfully!", 3000)
        else:
            self.status_bar.showMessage(f"Failed to update secret '{secret.name}'!", 3000)

    def on_llm_config_created(self, config_data):
        """Handle new LLM config creation"""
        config, name = config_data

        # Save to database
        if self.db.save_llm_config(config, name):
            # Reload configs from database
            self.llm_configs = self.db.load_llm_configs()

            # Update all components
            self.update_all_components()

            self.status_bar.showMessage(f"LLM configuration '{name}' saved successfully!", 3000)
        else:
            self.status_bar.showMessage(f"Failed to save LLM configuration '{name}'!", 3000)

    def on_project_created(self, project):
        """Handle new project creation"""
        # Save to database
        if self.db.save_project(project):
            # Reload projects from database
            self.projects = self.db.load_projects()

            # Update all components
            self.update_all_components()

            self.status_bar.showMessage(f"Project '{project.name}' saved successfully!", 3000)
        else:
            self.status_bar.showMessage(f"Failed to save project '{project.name}'!", 3000)

    def on_project_updated(self, project):
        """Handle project updates"""
        # Save to database
        if self.db.save_project(project):
            # Reload projects from database
            self.projects = self.db.load_projects()

            # Update all components
            self.update_all_components()

            self.status_bar.showMessage(f"Project '{project.name}' updated successfully!", 3000)
        else:
            self.status_bar.showMessage(f"Failed to update project '{project.name}'!", 3000)

    def on_project_deleted(self, project_id):
        """Handle project deletion"""
        # Delete from database
        if self.db.delete_project(project_id):
            # Reload projects from database
            self.projects = self.db.load_projects()

            # Update all components
            self.update_all_components()

            self.status_bar.showMessage("Project deleted successfully!", 3000)
        else:
            self.status_bar.showMessage("Failed to delete project!", 3000)

    def show_settings(self):
        """Show settings modal"""
        # Update settings modal with current data
        self.settings_modal.update_data(
            llm_configs=self.llm_configs,
            secrets=self.secrets
        )
        self.settings_modal.show()

    def show_welcome(self):
        """Show welcome message"""
        self.status_bar.showMessage("Welcome to Hyper Collaborative Agents!", 3000)
