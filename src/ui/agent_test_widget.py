from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QComboBox, QPushButton,
                            QGroupBox, QLabel, QMessageBox, QProgressBar,
                            QSplitter, QListWidget, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
import json
from datetime import datetime

from ..models.agent import Agent
from ..models.llm_config import LLMConfig

try:
    from ..rag_system import RAGSystem
except ImportError:
    RAGSystem = None


class AgentTestThread(QThread):
    """Thread for testing agent responses"""
    
    response_ready = pyqtSignal(bool, str, dict)  # success, response, metadata
    
    def __init__(self, agent, llm_config, user_input, projects=None, rag_system=None):
        super().__init__()
        self.agent = agent
        self.llm_config = llm_config
        self.user_input = user_input
        self.projects = projects or []
        self.rag_system = rag_system
    
    def run(self):
        try:
            # Simulate agent processing with RAG
            import time
            time.sleep(1)  # Simulate processing time

            # Use RAG if available and agent has projects
            rag_context = ""
            if self.rag_system and hasattr(self.agent, 'project_ids') and self.agent.project_ids:
                try:
                    rag_result = self.rag_system.query(self.user_input, self.agent.project_ids)
                    rag_context = rag_result.get('context', '')
                except Exception as e:
                    print(f"RAG query failed: {e}")

            # Generate response with RAG context
            response = self.generate_mock_response(rag_context)

            metadata = {
                "provider": self.llm_config.provider,
                "model": self.llm_config.model_name,
                "tokens_used": len(response.split()),  # Rough token count
                "response_time": 1.0,
                "timestamp": datetime.now().isoformat(),
                "rag_used": bool(rag_context),
                "projects_used": len(self.agent.project_ids) if hasattr(self.agent, 'project_ids') else 0
            }

            self.response_ready.emit(True, response, metadata)

        except Exception as e:
            self.response_ready.emit(False, f"Error: {str(e)}", {})

    def generate_mock_response(self, rag_context=""):
        """Generate a mock response based on user input and RAG context"""
        user_input_lower = self.user_input.lower()

        # If we have RAG context, use it
        if rag_context and rag_context != "No relevant documents found in the knowledge base.":
            return f"Based on the knowledge base documents:\n\n{rag_context[:500]}...\n\nRegarding your question '{self.user_input}', I found relevant information in the uploaded documents that should help answer your query."

        if "hello" in user_input_lower or "introduce" in user_input_lower:
            return f"Hello! I'm {self.agent.name}, {self.agent.description}. I'm here to help you with various tasks using my {self.agent.tone} communication style."

        elif "capabilities" in user_input_lower or "what can you do" in user_input_lower:
            capabilities_text = ", ".join(self.agent.capabilities) if self.agent.capabilities else "general assistance"
            tools_text = ", ".join(self.agent.tools) if self.agent.tools else "standard tools"
            return f"I have the following capabilities: {capabilities_text}. I can use these tools: {tools_text}. I'm configured to communicate in a {self.agent.tone} manner."

        elif "2 + 2" in user_input_lower or "math" in user_input_lower:
            return "2 + 2 equals 4. I can help you with various mathematical calculations and problem-solving tasks."

        elif "story" in user_input_lower:
            return "Once upon a time, in a digital realm, there lived an AI assistant who loved helping humans with their tasks. Every day brought new challenges and opportunities to learn and grow together."

        elif "quantum" in user_input_lower:
            return "Quantum computing is a revolutionary technology that uses quantum mechanical phenomena like superposition and entanglement to process information in ways that classical computers cannot."

        elif "weather" in user_input_lower:
            return "I don't have access to real-time weather data, but I can help you understand weather patterns, climate concepts, or suggest ways to check current weather conditions."

        elif "project" in user_input_lower or "plan" in user_input_lower:
            return "I'd be happy to help you plan your project! Let's start by defining your goals, timeline, and available resources. What type of project are you working on?"

        elif "harry potter" in user_input_lower:
            return "Harry Potter is a fictional character created by J.K. Rowling. In the story, he was born on July 31, 1980, at Godric's Hollow. However, since he's fictional, he doesn't have a real birthplace or date."

        else:
            return f"Thank you for your question: '{self.user_input}'. I'm {self.agent.name} and I'm here to help. Based on my configuration, I approach tasks with a {self.agent.tone} style and can assist you with various topics."


class AgentTestWidget(QWidget):
    """Widget for testing AI agents"""
    
    def __init__(self):
        super().__init__()
        self.agents = []
        self.llm_configs = []
        self.projects = []
        self.current_agent = None
        self.current_llm_config = None
        self.test_thread = None
        self.conversation_history = []

        # Initialize RAG system
        try:
            self.rag_system = RAGSystem() if RAGSystem else None
        except Exception:
            self.rag_system = None

        self.init_ui()
    
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        
        # Configuration section
        config_group = self.create_config_group()
        layout.addWidget(config_group)
        
        # Create splitter for test area
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - Input and controls
        left_widget = self.create_input_section()
        splitter.addWidget(left_widget)
        
        # Right side - Results
        right_widget = self.create_results_section()
        splitter.addWidget(right_widget)
        
        # Set splitter proportions
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
    
    def create_config_group(self):
        """Create configuration group"""
        group = QGroupBox("Test Configuration")
        layout = QFormLayout(group)
        
        # Agent selection
        self.agent_combo = QComboBox()
        self.agent_combo.setPlaceholderText("Select an agent...")
        self.agent_combo.currentTextChanged.connect(self.on_agent_changed)
        layout.addRow("Agent:", self.agent_combo)
        
        # LLM configuration selection
        self.llm_combo = QComboBox()
        self.llm_combo.setPlaceholderText("Select LLM configuration...")
        layout.addRow("LLM Config:", self.llm_combo)
        
        return group
    
    def create_input_section(self):
        """Create input section"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # User input group
        input_group = QGroupBox("User Input")
        input_layout = QVBoxLayout(input_group)
        
        self.user_input_edit = QTextEdit()
        self.user_input_edit.setPlaceholderText("Enter your message to the agent...")
        self.user_input_edit.setMaximumHeight(150)
        input_layout.addWidget(self.user_input_edit)
        
        # Input buttons
        input_button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("Test Agent")
        self.test_btn.clicked.connect(self.test_agent)
        input_button_layout.addWidget(self.test_btn)
        
        self.clear_input_btn = QPushButton("Clear Input")
        self.clear_input_btn.clicked.connect(self.clear_input)
        input_button_layout.addWidget(self.clear_input_btn)
        
        input_button_layout.addStretch()
        input_layout.addLayout(input_button_layout)
        
        layout.addWidget(input_group)
        
        # Quick test prompts
        prompts_group = QGroupBox("Quick Test Prompts")
        prompts_layout = QVBoxLayout(prompts_group)
        
        self.quick_prompts_list = QListWidget()
        self.quick_prompts_list.addItems([
            "Hello, introduce yourself",
            "What are your capabilities?",
            "Solve this problem: 2 + 2 = ?",
            "Write a short story",
            "Explain quantum computing",
            "What's the weather like?",
            "Help me plan a project",
            "Analyze this data"
        ])
        self.quick_prompts_list.itemDoubleClicked.connect(self.use_quick_prompt)
        self.quick_prompts_list.itemClicked.connect(self.use_quick_prompt)
        prompts_layout.addWidget(self.quick_prompts_list)
        
        layout.addWidget(prompts_group)
        
        return widget
    
    def create_results_section(self):
        """Create results section"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        
        # Response tab
        response_tab = QWidget()
        response_layout = QVBoxLayout(response_tab)
        
        # Response display
        self.response_display = QTextEdit()
        self.response_display.setReadOnly(True)
        self.response_display.setPlaceholderText("Agent responses will appear here...")
        
        # Set monospace font for better readability
        font = QFont("Consolas", 10)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.response_display.setFont(font)
        
        response_layout.addWidget(self.response_display)
        
        # Response buttons
        response_button_layout = QHBoxLayout()
        
        self.copy_response_btn = QPushButton("Copy Response")
        self.copy_response_btn.clicked.connect(self.copy_response)
        response_button_layout.addWidget(self.copy_response_btn)
        
        self.clear_response_btn = QPushButton("Clear Response")
        self.clear_response_btn.clicked.connect(self.clear_response)
        response_button_layout.addWidget(self.clear_response_btn)
        
        response_button_layout.addStretch()
        response_layout.addLayout(response_button_layout)
        
        self.results_tabs.addTab(response_tab, "Response")
        
        # Metadata tab
        metadata_tab = QWidget()
        metadata_layout = QVBoxLayout(metadata_tab)
        
        self.metadata_display = QTextEdit()
        self.metadata_display.setReadOnly(True)
        self.metadata_display.setPlaceholderText("Response metadata will appear here...")
        self.metadata_display.setFont(font)
        metadata_layout.addWidget(self.metadata_display)
        
        self.results_tabs.addTab(metadata_tab, "Metadata")
        
        # Conversation history tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        self.history_display = QTextEdit()
        self.history_display.setReadOnly(True)
        self.history_display.setPlaceholderText("Conversation history will appear here...")
        self.history_display.setFont(font)
        history_layout.addWidget(self.history_display)
        
        # History buttons
        history_button_layout = QHBoxLayout()
        
        self.clear_history_btn = QPushButton("Clear History")
        self.clear_history_btn.clicked.connect(self.clear_history)
        history_button_layout.addWidget(self.clear_history_btn)
        
        self.export_history_btn = QPushButton("Export History")
        self.export_history_btn.clicked.connect(self.export_history)
        history_button_layout.addWidget(self.export_history_btn)
        
        history_button_layout.addStretch()
        history_layout.addLayout(history_button_layout)
        
        self.results_tabs.addTab(history_tab, "History")
        
        layout.addWidget(self.results_tabs)
        
        return widget
    
    def on_agent_changed(self):
        """Handle agent selection change"""
        agent_name = self.agent_combo.currentText()
        self.current_agent = next((a for a in self.agents if a.name == agent_name), None)
        
        if self.current_agent:
            # Update LLM combo to show compatible configurations
            self.update_llm_combo()
    
    def update_llm_combo(self):
        """Update LLM configuration combo"""
        self.llm_combo.clear()

        if self.current_agent:
            # Add agent's LLM config if specified
            if hasattr(self.current_agent, 'llm_config_name') and self.current_agent.llm_config_name:
                self.llm_combo.addItem(f"Agent Default: {self.current_agent.llm_config_name}")

            # Add all available configurations
            for config in self.llm_configs:
                # Handle both dict and object formats
                if isinstance(config, dict):
                    config_name = f"{config['name']} ({config['provider']} - {config['model_name']})"
                else:
                    config_name = f"{config.name} ({config.provider} - {config.model_name})"
                self.llm_combo.addItem(config_name)
    
    def test_agent(self):
        """Test the selected agent"""
        if not self.validate_test_config():
            return
        
        user_input = self.user_input_edit.toPlainText().strip()
        if not user_input:
            QMessageBox.warning(self, "Warning", "Please enter a message to test!")
            return
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.test_btn.setEnabled(False)
        
        # Get selected LLM configuration
        llm_config_name = self.llm_combo.currentText()

        # Find the actual LLM config
        selected_config = None
        for config in self.llm_configs:
            if isinstance(config, dict):
                config_display_name = f"{config['name']} ({config['provider']} - {config['model_name']})"
                if config_display_name == llm_config_name:
                    selected_config = config
                    break

        if not selected_config:
            QMessageBox.warning(self, "Error", "Could not find the selected LLM configuration!")
            return

        # Create LLM config object for testing
        from ..models.llm_config import LLMProvider
        mock_llm_config = LLMConfig(
            id=selected_config['id'],
            provider=LLMProvider(selected_config['provider']),
            model_name=selected_config['model_name']
        )
        
        # Start test thread with RAG support
        self.test_thread = AgentTestThread(
            self.current_agent,
            mock_llm_config,
            user_input,
            projects=self.projects,
            rag_system=self.rag_system
        )
        self.test_thread.response_ready.connect(self.on_test_result)
        self.test_thread.start()
    
    def validate_test_config(self):
        """Validate test configuration"""
        if not self.current_agent:
            QMessageBox.warning(self, "Warning", "Please select an agent!")
            return False
        
        if not self.llm_combo.currentText():
            QMessageBox.warning(self, "Warning", "Please select an LLM configuration!")
            return False
        
        return True
    
    def on_test_result(self, success, response, metadata):
        """Handle test result"""
        self.progress_bar.setVisible(False)
        self.test_btn.setEnabled(True)
        
        if success:
            # Display response
            self.response_display.setPlainText(response)
            
            # Display metadata
            metadata_text = json.dumps(metadata, indent=2)
            self.metadata_display.setPlainText(metadata_text)
            
            # Add to conversation history
            user_input = self.user_input_edit.toPlainText().strip()
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            history_entry = {
                "timestamp": timestamp,
                "user_input": user_input,
                "agent_response": response,
                "metadata": metadata
            }
            
            self.conversation_history.append(history_entry)
            self.update_history_display()
            
            # Switch to response tab
            self.results_tabs.setCurrentIndex(0)
            
        else:
            QMessageBox.critical(self, "Test Failed", response)
    
    def update_history_display(self):
        """Update conversation history display"""
        history_text = ""
        
        for i, entry in enumerate(self.conversation_history, 1):
            history_text += f"=== Conversation {i} - {entry['timestamp']} ===\n"
            history_text += f"User: {entry['user_input']}\n\n"
            history_text += f"Agent: {entry['agent_response']}\n"
            history_text += "=" * 50 + "\n\n"
        
        self.history_display.setPlainText(history_text)
    
    def use_quick_prompt(self, item):
        """Use selected quick prompt"""
        self.user_input_edit.setPlainText(item.text())
    
    def clear_input(self):
        """Clear user input"""
        self.user_input_edit.clear()
    
    def clear_response(self):
        """Clear response display"""
        self.response_display.clear()
        self.metadata_display.clear()
    
    def clear_history(self):
        """Clear conversation history"""
        reply = QMessageBox.question(self, "Clear History",
                                   "Are you sure you want to clear the conversation history?",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.conversation_history.clear()
            self.history_display.clear()
    
    def copy_response(self):
        """Copy response to clipboard"""
        from PyQt5.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(self.response_display.toPlainText())
        QMessageBox.information(self, "Copied", "Response copied to clipboard!")
    
    def export_history(self):
        """Export conversation history"""
        if not self.conversation_history:
            QMessageBox.information(self, "No History", "No conversation history to export!")
            return
        
        # TODO: Implement history export functionality
        QMessageBox.information(self, "Export History", "History export functionality will be implemented!")
    
    def update_data(self, agents=None, llm_configs=None, projects=None):
        """Update available data"""
        if agents is not None:
            self.agents = agents
            self.agent_combo.clear()
            self.agent_combo.addItems([agent.name for agent in agents])

        if llm_configs is not None:
            self.llm_configs = llm_configs

        if projects is not None:
            self.projects = projects
