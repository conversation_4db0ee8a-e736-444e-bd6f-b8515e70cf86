"""
Projects Widget

Manages projects with knowledge bases and file uploads for RAG
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QPushButton, QGroupBox,
                            QListWidget, QLabel, QMessageBox, QFileDialog,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QProgressBar, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal, QThread
from PyQt5.QtGui import QFont
import os
import hashlib
import shutil
from datetime import datetime

try:
    from ..models.project import Project, ProjectFile
except ImportError:
    from models.project import Project, ProjectFile


class FileProcessingThread(QThread):
    """Thread for processing uploaded files"""
    
    progress_updated = pyqtSignal(int)  # progress percentage
    file_processed = pyqtSignal(str, bool, str)  # file_id, success, message
    
    def __init__(self, project_file, storage_path):
        super().__init__()
        self.project_file = project_file
        self.storage_path = storage_path
    
    def run(self):
        try:
            # Simulate file processing for RAG
            import time
            for i in range(101):
                time.sleep(0.02)  # Simulate processing time
                self.progress_updated.emit(i)
            
            # Mark as processed
            self.file_processed.emit(self.project_file.id, True, "File processed successfully")
            
        except Exception as e:
            self.file_processed.emit(self.project_file.id, False, f"Processing failed: {str(e)}")


class ProjectsWidget(QWidget):
    """Widget for managing projects with knowledge bases"""
    
    project_created = pyqtSignal(Project)
    project_updated = pyqtSignal(Project)
    project_deleted = pyqtSignal(str)  # project_id
    
    def __init__(self):
        super().__init__()
        self.projects = []
        self.current_project = None
        self.storage_base_path = os.path.expanduser("~/.hyper_collaborative_agents_files")
        self.processing_thread = None
        
        # Ensure storage directory exists
        os.makedirs(self.storage_base_path, exist_ok=True)
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        
        # Create splitter for left/right layout
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - Projects list and management
        left_widget = self.create_projects_section()
        splitter.addWidget(left_widget)
        
        # Right side - Project details and files
        right_widget = self.create_project_details_section()
        splitter.addWidget(right_widget)
        
        # Set splitter proportions
        splitter.setSizes([300, 700])
        layout.addWidget(splitter)
    
    def create_projects_section(self):
        """Create projects list section"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Projects list
        projects_group = QGroupBox("Projects")
        projects_layout = QVBoxLayout(projects_group)
        
        self.projects_list = QListWidget()
        self.projects_list.itemClicked.connect(self.on_project_selected)
        projects_layout.addWidget(self.projects_list)
        
        # Project buttons
        projects_btn_layout = QHBoxLayout()
        
        self.new_project_btn = QPushButton("New Project")
        self.new_project_btn.clicked.connect(self.new_project)
        projects_btn_layout.addWidget(self.new_project_btn)
        
        self.delete_project_btn = QPushButton("Delete Project")
        self.delete_project_btn.clicked.connect(self.delete_project)
        self.delete_project_btn.setEnabled(False)
        projects_btn_layout.addWidget(self.delete_project_btn)
        
        projects_layout.addLayout(projects_btn_layout)
        layout.addWidget(projects_group)
        
        # Project form
        form_group = self.create_project_form()
        layout.addWidget(form_group)
        
        return widget
    
    def create_project_form(self):
        """Create project form"""
        group = QGroupBox("Project Details")
        layout = QFormLayout(group)
        
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText("Enter project name...")
        layout.addRow("Name:", self.project_name_edit)
        
        self.project_description_edit = QTextEdit()
        self.project_description_edit.setMaximumHeight(80)
        self.project_description_edit.setPlaceholderText("Enter project description...")
        layout.addRow("Description:", self.project_description_edit)
        
        # Save button
        self.save_project_btn = QPushButton("Save Project")
        self.save_project_btn.clicked.connect(self.save_project)
        layout.addRow("", self.save_project_btn)
        
        return group
    
    def create_project_details_section(self):
        """Create project details and files section"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Project info
        info_group = QGroupBox("Project Information")
        info_layout = QVBoxLayout(info_group)
        
        self.project_info_label = QLabel("Select a project to view details")
        self.project_info_label.setWordWrap(True)
        info_layout.addWidget(self.project_info_label)
        
        layout.addWidget(info_group)
        
        # Files section
        files_group = self.create_files_section()
        layout.addWidget(files_group)
        
        return widget
    
    def create_files_section(self):
        """Create files management section"""
        group = QGroupBox("Knowledge Base Files")
        layout = QVBoxLayout(group)
        
        # File upload section
        upload_layout = QHBoxLayout()
        
        self.upload_btn = QPushButton("Upload Files")
        self.upload_btn.clicked.connect(self.upload_files)
        self.upload_btn.setEnabled(False)
        upload_layout.addWidget(self.upload_btn)
        
        self.process_btn = QPushButton("Process for RAG")
        self.process_btn.clicked.connect(self.process_files)
        self.process_btn.setEnabled(False)
        upload_layout.addWidget(self.process_btn)
        
        upload_layout.addStretch()
        layout.addLayout(upload_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Files table
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(6)
        self.files_table.setHorizontalHeaderLabels([
            "Name", "Type", "Size", "Uploaded", "Processed", "Actions"
        ])
        
        # Configure table
        header = self.files_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.files_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        layout.addWidget(self.files_table)
        
        return group
    
    def new_project(self):
        """Create a new project"""
        self.current_project = None
        self.clear_form()
        self.update_ui_state()
    
    def clear_form(self):
        """Clear project form"""
        self.project_name_edit.clear()
        self.project_description_edit.clear()
        self.files_table.setRowCount(0)
        self.project_info_label.setText("Enter project details and save")
    
    def save_project(self):
        """Save the current project"""
        name = self.project_name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Validation Error", "Project name is required!")
            return
        
        description = self.project_description_edit.toPlainText().strip()
        
        try:
            if self.current_project:
                # Update existing project
                self.current_project.name = name
                self.current_project.description = description
                self.current_project.updated_at = datetime.now()
                self.project_updated.emit(self.current_project)
            else:
                # Create new project
                project = Project(
                    name=name,
                    description=description,
                    knowledge_base_path=os.path.join(self.storage_base_path, name.replace(" ", "_"))
                )
                
                # Create project directory
                os.makedirs(project.knowledge_base_path, exist_ok=True)
                
                self.projects.append(project)
                self.current_project = project
                self.project_created.emit(project)
            
            self.update_projects_list()
            self.update_project_info()
            self.update_ui_state()
            
            QMessageBox.information(self, "Success", f"Project '{name}' saved successfully!")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save project: {str(e)}")
    
    def on_project_selected(self, item):
        """Handle project selection"""
        project_name = item.text().split(" - ")[0]  # Remove file count from display
        self.current_project = next((p for p in self.projects if p.name == project_name), None)
        
        if self.current_project:
            self.load_project_to_form()
            self.update_ui_state()
    
    def load_project_to_form(self):
        """Load project data to form"""
        if not self.current_project:
            return
        
        self.project_name_edit.setText(self.current_project.name)
        self.project_description_edit.setPlainText(self.current_project.description)
        self.update_project_info()
        self.update_files_table()
    
    def update_projects_list(self):
        """Update the projects list"""
        self.projects_list.clear()
        for project in self.projects:
            file_count = len(project.files)
            processed_count = len(project.get_processed_files())
            display_text = f"{project.name} - {file_count} files ({processed_count} processed)"
            self.projects_list.addItem(display_text)
    
    def update_project_info(self):
        """Update project information display"""
        if not self.current_project:
            self.project_info_label.setText("Select a project to view details")
            return
        
        info = []
        info.append(f"<b>Name:</b> {self.current_project.name}")
        info.append(f"<b>Description:</b> {self.current_project.description}")
        info.append(f"<b>Files:</b> {self.current_project.get_file_count()}")
        info.append(f"<b>Processed:</b> {len(self.current_project.get_processed_files())}")
        info.append(f"<b>Total Size:</b> {self.format_file_size(self.current_project.get_total_size())}")
        info.append(f"<b>Created:</b> {self.current_project.created_at.strftime('%Y-%m-%d %H:%M')}")
        info.append(f"<b>Updated:</b> {self.current_project.updated_at.strftime('%Y-%m-%d %H:%M')}")
        
        self.project_info_label.setText("<br>".join(info))
    
    def update_files_table(self):
        """Update the files table"""
        if not self.current_project:
            self.files_table.setRowCount(0)
            return
        
        self.files_table.setRowCount(len(self.current_project.files))
        
        for row, file in enumerate(self.current_project.files):
            # Name
            name_item = QTableWidgetItem(file.name)
            self.files_table.setItem(row, 0, name_item)
            
            # Type
            type_item = QTableWidgetItem(file.file_type)
            self.files_table.setItem(row, 1, type_item)
            
            # Size
            size_item = QTableWidgetItem(self.format_file_size(file.file_size))
            self.files_table.setItem(row, 2, size_item)
            
            # Uploaded
            uploaded_item = QTableWidgetItem(file.uploaded_at.strftime("%Y-%m-%d %H:%M"))
            self.files_table.setItem(row, 3, uploaded_item)
            
            # Processed
            processed_item = QTableWidgetItem("✓" if file.processed else "✗")
            self.files_table.setItem(row, 4, processed_item)
            
            # Actions (placeholder)
            actions_item = QTableWidgetItem("Remove")
            self.files_table.setItem(row, 5, actions_item)
    
    def upload_files(self):
        """Upload files to current project"""
        if not self.current_project:
            return
        
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Select Files for Knowledge Base",
            "",
            "All Files (*);;Text Files (*.txt);;PDF Files (*.pdf);;Word Documents (*.docx);;Markdown Files (*.md)"
        )
        
        if not file_paths:
            return
        
        try:
            for file_path in file_paths:
                self.add_file_to_project(file_path)
            
            self.update_files_table()
            self.update_project_info()
            self.update_projects_list()
            
            QMessageBox.information(self, "Success", f"Uploaded {len(file_paths)} files successfully!")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to upload files: {str(e)}")
    
    def add_file_to_project(self, file_path):
        """Add a file to the current project"""
        if not self.current_project:
            return
        
        # Get file info
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        file_type = os.path.splitext(file_name)[1].lower()
        
        # Calculate file hash
        with open(file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        
        # Check for duplicates
        for existing_file in self.current_project.files:
            if existing_file.content_hash == file_hash:
                QMessageBox.warning(self, "Duplicate File", f"File '{file_name}' already exists in project!")
                return
        
        # Copy file to project directory
        project_file_path = os.path.join(self.current_project.knowledge_base_path, file_name)
        shutil.copy2(file_path, project_file_path)
        
        # Create project file
        project_file = ProjectFile(
            name=file_name,
            file_path=project_file_path,
            file_type=file_type,
            file_size=file_size,
            content_hash=file_hash
        )
        
        self.current_project.add_file(project_file)
    
    def process_files(self):
        """Process files for RAG"""
        if not self.current_project:
            return
        
        unprocessed_files = [f for f in self.current_project.files if not f.processed]
        if not unprocessed_files:
            QMessageBox.information(self, "Info", "All files are already processed!")
            return
        
        # Process first unprocessed file (in real implementation, process all)
        file_to_process = unprocessed_files[0]
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.process_btn.setEnabled(False)
        
        # Start processing thread
        self.processing_thread = FileProcessingThread(file_to_process, self.current_project.knowledge_base_path)
        self.processing_thread.progress_updated.connect(self.on_processing_progress)
        self.processing_thread.file_processed.connect(self.on_file_processed)
        self.processing_thread.start()
    
    def on_processing_progress(self, progress):
        """Handle processing progress"""
        self.progress_bar.setValue(progress)
    
    def on_file_processed(self, file_id, success, message):
        """Handle file processing completion"""
        self.progress_bar.setVisible(False)
        self.process_btn.setEnabled(True)
        
        if success:
            # Mark file as processed
            for file in self.current_project.files:
                if file.id == file_id:
                    file.processed = True
                    break
            
            self.update_files_table()
            self.update_project_info()
            self.update_projects_list()
            
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.critical(self, "Error", message)
    
    def delete_project(self):
        """Delete the current project"""
        if not self.current_project:
            return
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete project '{self.current_project.name}'?\nThis will also delete all uploaded files.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # Remove project directory
                if os.path.exists(self.current_project.knowledge_base_path):
                    shutil.rmtree(self.current_project.knowledge_base_path)
                
                # Remove from list
                self.projects.remove(self.current_project)
                self.project_deleted.emit(self.current_project.id)
                
                self.current_project = None
                self.update_projects_list()
                self.clear_form()
                self.update_ui_state()
                
                QMessageBox.information(self, "Success", "Project deleted successfully!")
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to delete project: {str(e)}")
    
    def update_ui_state(self):
        """Update UI state based on current selection"""
        has_project = self.current_project is not None
        
        self.delete_project_btn.setEnabled(has_project)
        self.upload_btn.setEnabled(has_project)
        self.process_btn.setEnabled(has_project and any(not f.processed for f in (self.current_project.files if has_project else [])))
    
    def update_projects_data(self, projects):
        """Update projects data from external source"""
        self.projects = projects
        self.update_projects_list()
    
    @staticmethod
    def format_file_size(size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
