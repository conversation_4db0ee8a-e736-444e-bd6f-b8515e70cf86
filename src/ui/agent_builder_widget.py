from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox,
                            QComboBox, QListWidget, QPushButton, QGroupBox,
                            QLabel, QMessageBox, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal

from ..models.agent import Agent, ToneType
from ..models.llm_config import LLMProvider
import uuid


class AgentBuilderWidget(QWidget):
    """Widget for building and configuring AI agents"""
    
    agent_created = pyqtSignal(Agent)
    
    def __init__(self):
        super().__init__()
        self.current_agent = None
        self.agents = []
        self.projects = []
        self.init_ui()
    
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)

        # Existing agents section
        agents_group = self.create_agents_list_group()
        layout.addWidget(agents_group)

        # Create scroll area for the form
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # Basic Information Group
        basic_group = self.create_basic_info_group()
        scroll_layout.addWidget(basic_group)
        
        # Prompts Group
        prompts_group = self.create_prompts_group()
        scroll_layout.addWidget(prompts_group)
        
        # Configuration Group
        config_group = self.create_config_group()
        scroll_layout.addWidget(config_group)
        
        # Tools and Capabilities Group
        tools_group = self.create_tools_group()
        scroll_layout.addWidget(tools_group)
        
        # LLM Settings Group
        llm_group = self.create_llm_group()
        scroll_layout.addWidget(llm_group)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.new_btn = QPushButton("New Agent")
        self.new_btn.clicked.connect(self.new_agent)
        button_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton("Save Agent")
        self.save_btn.clicked.connect(self.save_agent)
        button_layout.addWidget(self.save_btn)
        
        self.test_btn = QPushButton("Test Agent")
        self.test_btn.clicked.connect(self.test_agent)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        self.prompt_create_btn = QPushButton("Create Agent from Prompt")
        self.prompt_create_btn.clicked.connect(self.create_from_prompt)
        button_layout.addWidget(self.prompt_create_btn)
        
        layout.addLayout(button_layout)

    def create_agents_list_group(self):
        """Create existing agents list group"""
        group = QGroupBox("Existing Agents")
        layout = QVBoxLayout(group)

        self.agents_list = QListWidget()
        self.agents_list.setMaximumHeight(100)
        self.agents_list.itemClicked.connect(self.on_agent_selected)
        layout.addWidget(self.agents_list)

        agents_btn_layout = QHBoxLayout()
        self.load_agent_btn = QPushButton("Load Selected")
        self.load_agent_btn.clicked.connect(self.load_selected_agent)
        self.load_agent_btn.setEnabled(False)
        agents_btn_layout.addWidget(self.load_agent_btn)

        self.delete_agent_btn = QPushButton("Delete Selected")
        self.delete_agent_btn.clicked.connect(self.delete_selected_agent)
        self.delete_agent_btn.setEnabled(False)
        agents_btn_layout.addWidget(self.delete_agent_btn)

        agents_btn_layout.addStretch()
        layout.addLayout(agents_btn_layout)

        return group

    def create_basic_info_group(self):
        """Create basic information group"""
        group = QGroupBox("Basic Information")
        layout = QFormLayout(group)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter agent name...")
        layout.addRow("Name:", self.name_edit)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("Enter agent description...")
        layout.addRow("Description:", self.description_edit)
        
        return group
    
    def create_prompts_group(self):
        """Create prompts configuration group"""
        group = QGroupBox("Prompts")
        layout = QFormLayout(group)
        
        self.system_prompt_edit = QTextEdit()
        self.system_prompt_edit.setMaximumHeight(120)
        self.system_prompt_edit.setPlaceholderText("Enter system prompt...")
        layout.addRow("System Prompt:", self.system_prompt_edit)
        
        self.user_prompt_edit = QTextEdit()
        self.user_prompt_edit.setMaximumHeight(80)
        self.user_prompt_edit.setPlaceholderText("Enter default user prompt (optional)...")
        layout.addRow("User Prompt:", self.user_prompt_edit)
        
        return group
    
    def create_config_group(self):
        """Create configuration group"""
        group = QGroupBox("Configuration")
        layout = QFormLayout(group)
        
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(1, 100000)
        self.max_tokens_spin.setValue(1000)
        layout.addRow("Max Tokens:", self.max_tokens_spin)
        
        self.tone_combo = QComboBox()
        self.tone_combo.addItems([tone.value for tone in ToneType])
        layout.addRow("Tone:", self.tone_combo)
        
        self.paraphrase_spin = QDoubleSpinBox()
        self.paraphrase_spin.setRange(0.0, 1.0)
        self.paraphrase_spin.setSingleStep(0.1)
        self.paraphrase_spin.setValue(0.0)
        layout.addRow("Paraphrase Randomization:", self.paraphrase_spin)
        
        return group
    
    def create_tools_group(self):
        """Create tools and capabilities group"""
        group = QGroupBox("Tools & Capabilities")
        layout = QVBoxLayout(group)
        
        # Tools section
        tools_layout = QHBoxLayout()
        tools_layout.addWidget(QLabel("Tools:"))
        
        self.tools_list = QListWidget()
        self.tools_list.setMaximumHeight(100)
        tools_layout.addWidget(self.tools_list)
        
        tools_btn_layout = QVBoxLayout()
        self.add_tool_btn = QPushButton("Add Tool")
        self.add_tool_btn.clicked.connect(self.add_tool)
        tools_btn_layout.addWidget(self.add_tool_btn)
        
        self.remove_tool_btn = QPushButton("Remove Tool")
        self.remove_tool_btn.clicked.connect(self.remove_tool)
        tools_btn_layout.addWidget(self.remove_tool_btn)
        
        tools_layout.addLayout(tools_btn_layout)
        layout.addLayout(tools_layout)
        
        # Capabilities section
        caps_layout = QHBoxLayout()
        caps_layout.addWidget(QLabel("Capabilities:"))
        
        self.capabilities_list = QListWidget()
        self.capabilities_list.setMaximumHeight(100)
        caps_layout.addWidget(self.capabilities_list)
        
        caps_btn_layout = QVBoxLayout()
        self.add_cap_btn = QPushButton("Add Capability")
        self.add_cap_btn.clicked.connect(self.add_capability)
        caps_btn_layout.addWidget(self.add_cap_btn)
        
        self.remove_cap_btn = QPushButton("Remove Capability")
        self.remove_cap_btn.clicked.connect(self.remove_capability)
        caps_btn_layout.addWidget(self.remove_cap_btn)
        
        caps_layout.addLayout(caps_btn_layout)
        layout.addLayout(caps_layout)
        
        return group
    
    def create_llm_group(self):
        """Create LLM settings group"""
        group = QGroupBox("LLM & Knowledge Settings")
        layout = QFormLayout(group)

        self.llm_config_combo = QComboBox()
        self.llm_config_combo.setPlaceholderText("Select LLM configuration...")
        layout.addRow("LLM Configuration:", self.llm_config_combo)

        # Projects selection
        self.projects_list = QListWidget()
        self.projects_list.setMaximumHeight(100)
        self.projects_list.setSelectionMode(QListWidget.MultiSelection)
        layout.addRow("Knowledge Base Projects:", self.projects_list)

        return group
    
    def new_agent(self):
        """Create a new agent"""
        self.clear_form()
        self.current_agent = None
    
    def clear_form(self):
        """Clear all form fields"""
        self.name_edit.clear()
        self.description_edit.clear()
        self.system_prompt_edit.clear()
        self.user_prompt_edit.clear()
        self.max_tokens_spin.setValue(1000)
        self.tone_combo.setCurrentIndex(0)
        self.paraphrase_spin.setValue(0.0)
        self.tools_list.clear()
        self.capabilities_list.clear()
        self.llm_config_combo.setCurrentIndex(-1)
        self.projects_list.clearSelection()
    
    def save_agent(self):
        """Save the current agent"""
        if not self.validate_form():
            return
        
        try:
            agent_id = self.current_agent.id if self.current_agent else str(uuid.uuid4())
            
            agent = Agent(
                id=agent_id,
                name=self.name_edit.text(),
                description=self.description_edit.toPlainText(),
                system_prompt=self.system_prompt_edit.toPlainText(),
                user_prompt=self.user_prompt_edit.toPlainText() or None,
                max_tokens=self.max_tokens_spin.value(),
                tools=[self.tools_list.item(i).text() for i in range(self.tools_list.count())],
                capabilities=[self.capabilities_list.item(i).text() for i in range(self.capabilities_list.count())],
                tone=ToneType(self.tone_combo.currentText()),
                paraphrase_randomization=self.paraphrase_spin.value(),
                llm_config_name=self.llm_config_combo.currentText() or None,
            project_ids=[self.projects[item.row()].id for item in self.projects_list.selectedItems()]
            )
            
            self.current_agent = agent
            self.agent_created.emit(agent)
            
            QMessageBox.information(self, "Success", f"Agent '{agent.name}' saved successfully!")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save agent: {str(e)}")
    
    def validate_form(self):
        """Validate form inputs"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Validation Error", "Agent name is required!")
            return False

        if not self.system_prompt_edit.toPlainText().strip():
            QMessageBox.warning(self, "Validation Error", "System prompt is required!")
            return False

        if not self.llm_config_combo.currentText():
            QMessageBox.warning(self, "Validation Error", "Please select an LLM configuration!")
            return False

        return True
    
    def test_agent(self):
        """Test the current agent"""
        if not self.current_agent:
            QMessageBox.warning(self, "Warning", "Please save the agent first!")
            return
        
        # TODO: Implement agent testing
        QMessageBox.information(self, "Test Agent", "Agent testing functionality will be implemented!")
    
    def create_from_prompt(self):
        """Create agent from natural language prompt"""
        # TODO: Implement prompt-based agent creation
        QMessageBox.information(self, "Create from Prompt", "Prompt-based agent creation will be implemented!")
    
    def add_tool(self):
        """Add a tool to the agent"""
        from PyQt5.QtWidgets import QInputDialog
        tool, ok = QInputDialog.getText(self, "Add Tool", "Enter tool name:")
        if ok and tool.strip():
            self.tools_list.addItem(tool.strip())
    
    def remove_tool(self):
        """Remove selected tool"""
        current_row = self.tools_list.currentRow()
        if current_row >= 0:
            self.tools_list.takeItem(current_row)
    
    def add_capability(self):
        """Add a capability to the agent"""
        from PyQt5.QtWidgets import QInputDialog
        capability, ok = QInputDialog.getText(self, "Add Capability", "Enter capability:")
        if ok and capability.strip():
            self.capabilities_list.addItem(capability.strip())
    
    def remove_capability(self):
        """Remove selected capability"""
        current_row = self.capabilities_list.currentRow()
        if current_row >= 0:
            self.capabilities_list.takeItem(current_row)

    def update_agents_list(self, agents):
        """Update the agents list"""
        self.agents = agents
        self.agents_list.clear()
        for agent in agents:
            self.agents_list.addItem(f"{agent.name} - {agent.description[:50]}...")

    def update_projects_list(self, projects):
        """Update the projects list"""
        self.projects = projects
        self.projects_list.clear()
        for project in projects:
            file_count = len(project.files)
            self.projects_list.addItem(f"{project.name} ({file_count} files)")

    def on_agent_selected(self, item):
        """Handle agent selection"""
        self.load_agent_btn.setEnabled(True)
        self.delete_agent_btn.setEnabled(True)

    def load_selected_agent(self):
        """Load selected agent into form"""
        current_row = self.agents_list.currentRow()
        if current_row < 0 or current_row >= len(self.agents):
            return

        agent = self.agents[current_row]
        self.current_agent = agent

        # Load agent data into form
        self.name_edit.setText(agent.name)
        self.description_edit.setPlainText(agent.description)
        self.system_prompt_edit.setPlainText(agent.system_prompt)
        self.user_prompt_edit.setPlainText(agent.user_prompt or "")
        self.max_tokens_spin.setValue(agent.max_tokens)

        # Set tone
        tone_index = self.tone_combo.findText(agent.tone)
        if tone_index >= 0:
            self.tone_combo.setCurrentIndex(tone_index)

        self.paraphrase_spin.setValue(agent.paraphrase_randomization)

        # Load tools
        self.tools_list.clear()
        for tool in agent.tools:
            self.tools_list.addItem(tool)

        # Load capabilities
        self.capabilities_list.clear()
        for capability in agent.capabilities:
            self.capabilities_list.addItem(capability)

        # Load LLM config
        if hasattr(agent, 'llm_config_name') and agent.llm_config_name:
            config_index = self.llm_config_combo.findText(agent.llm_config_name)
            if config_index >= 0:
                self.llm_config_combo.setCurrentIndex(config_index)

        # Load selected projects
        if hasattr(agent, 'project_ids') and agent.project_ids:
            for i, project in enumerate(self.projects):
                if project.id in agent.project_ids:
                    self.projects_list.item(i).setSelected(True)

    def delete_selected_agent(self):
        """Delete selected agent"""
        current_row = self.agents_list.currentRow()
        if current_row < 0 or current_row >= len(self.agents):
            return

        agent = self.agents[current_row]

        reply = QMessageBox.question(self, "Confirm Delete",
                                   f"Are you sure you want to delete agent '{agent.name}'?",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # TODO: Emit signal to delete from database
            QMessageBox.information(self, "Delete", "Agent deletion will be implemented!")
