from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid


class ProjectFile(BaseModel):
    """Model for files in a project"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique file identifier")
    name: str = Field(..., description="Original filename")
    file_path: str = Field(..., description="Path to stored file")
    file_type: str = Field(..., description="File type/extension")
    file_size: int = Field(..., description="File size in bytes")
    content_hash: str = Field(..., description="Hash of file content for deduplication")
    uploaded_at: datetime = Field(default_factory=datetime.now, description="Upload timestamp")
    processed: bool = Field(default=False, description="Whether file has been processed for RAG")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional file metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class Project(BaseModel):
    """Model for projects with knowledge base"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique project identifier")
    name: str = Field(..., description="Project name")
    description: str = Field(default="", description="Project description")
    files: List[ProjectFile] = Field(default_factory=list, description="Files in the project")
    knowledge_base_path: str = Field(default="", description="Path to processed knowledge base")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional project metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def add_file(self, file: ProjectFile) -> None:
        """Add a file to the project"""
        self.files.append(file)
        self.updated_at = datetime.now()
    
    def remove_file(self, file_id: str) -> bool:
        """Remove a file from the project"""
        for i, file in enumerate(self.files):
            if file.id == file_id:
                del self.files[i]
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_file(self, file_id: str) -> Optional[ProjectFile]:
        """Get a file by ID"""
        for file in self.files:
            if file.id == file_id:
                return file
        return None
    
    def get_total_size(self) -> int:
        """Get total size of all files in bytes"""
        return sum(file.file_size for file in self.files)
    
    def get_file_count(self) -> int:
        """Get number of files in project"""
        return len(self.files)
    
    def get_processed_files(self) -> List[ProjectFile]:
        """Get list of processed files"""
        return [file for file in self.files if file.processed]
