from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum


class ToneType(str, Enum):
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    CREATIVE = "creative"
    ANALYTICAL = "analytical"
    FRIENDLY = "friendly"
    FORMAL = "formal"


class Agent(BaseModel):
    """Data model for AI agents"""
    
    id: str = Field(..., description="Unique identifier for the agent")
    name: str = Field(..., description="Agent name")
    description: str = Field(..., description="Agent description")
    system_prompt: str = Field(..., description="System prompt for the agent")
    user_prompt: Optional[str] = Field(None, description="Default user prompt")
    max_tokens: int = Field(default=1000, description="Maximum tokens for responses")
    tools: List[str] = Field(default_factory=list, description="Available tools")
    capabilities: List[str] = Field(default_factory=list, description="Agent capabilities")
    tone: ToneType = Field(default=ToneType.PROFESSIONAL, description="Agent tone")
    paraphrase_randomization: float = Field(default=0.0, ge=0.0, le=1.0, description="Paraphrase randomization level")
    llm_config_name: Optional[str] = Field(None, description="Name of the LLM configuration to use")
    project_ids: List[str] = Field(default_factory=list, description="List of project IDs for knowledge base")
    # Legacy fields for backward compatibility
    default_llm_provider: str = Field(default="", description="Default LLM provider (legacy)")
    default_llm_model: str = Field(default="", description="Default LLM model (legacy)")
    secret_name: Optional[str] = Field(None, description="Associated secret name for API keys")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        use_enum_values = True
