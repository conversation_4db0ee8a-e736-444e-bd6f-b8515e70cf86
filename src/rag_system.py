"""
RAG (Retrieval-Augmented Generation) System using FAISS and LangGraph
"""

import os
import json
import pickle
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging

try:
    import faiss
    import numpy as np
    from sentence_transformers import SentenceTransformer
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.docstore.document import Document
    from langchain.document_loaders import <PERSON>Loader, PyPDFLoader
    from langgraph.graph import Graph, StateGraph
    from langgraph.graph.message import add_messages
    from typing_extensions import TypedDict
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"RAG dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False

try:
    from .models.project import Project, ProjectFile
except ImportError:
    from models.project import Project, ProjectFile


class RAGState(TypedDict):
    """State for RAG workflow"""
    query: str
    documents: List[Document]
    context: str
    response: str
    metadata: Dict[str, Any]


class RAGSystem:
    """RAG system using FAISS for vector storage and LangGraph for workflow"""
    
    def __init__(self, storage_path: str = None):
        if not DEPENDENCIES_AVAILABLE:
            raise ImportError("RAG dependencies not installed. Run: pip install faiss-cpu sentence-transformers langchain")
        
        self.storage_path = storage_path or os.path.expanduser("~/.hyper_collaborative_agents_rag")
        os.makedirs(self.storage_path, exist_ok=True)
        
        # Initialize components
        self.embeddings_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        
        # FAISS index
        self.index = None
        self.documents = []
        self.document_metadata = []
        
        # LangGraph workflow
        self.workflow = self._create_workflow()
        
        # Load existing index if available
        self.load_index()
    
    def _create_workflow(self) -> StateGraph:
        """Create LangGraph workflow for RAG"""
        workflow = StateGraph(RAGState)
        
        # Add nodes
        workflow.add_node("retrieve", self._retrieve_documents)
        workflow.add_node("generate_context", self._generate_context)
        workflow.add_node("generate_response", self._generate_response)
        
        # Add edges
        workflow.add_edge("retrieve", "generate_context")
        workflow.add_edge("generate_context", "generate_response")
        
        # Set entry point
        workflow.set_entry_point("retrieve")
        workflow.set_finish_point("generate_response")
        
        return workflow.compile()
    
    def process_project_files(self, project: Project) -> bool:
        """Process all files in a project for RAG"""
        try:
            documents = []
            
            for project_file in project.files:
                if project_file.processed:
                    continue
                
                # Load and process file
                file_documents = self._load_file(project_file)
                if file_documents:
                    documents.extend(file_documents)
                    project_file.processed = True
            
            if documents:
                # Add to vector store
                self._add_documents(documents, project.id)
                self.save_index()
                return True
            
            return False
            
        except Exception as e:
            logging.error(f"Error processing project files: {e}")
            return False
    
    def _load_file(self, project_file: ProjectFile) -> List[Document]:
        """Load and split a single file"""
        try:
            documents = []
            
            if project_file.file_type.lower() == '.txt':
                loader = TextLoader(project_file.file_path, encoding='utf-8')
                docs = loader.load()
                documents.extend(docs)
            
            elif project_file.file_type.lower() == '.pdf':
                loader = PyPDFLoader(project_file.file_path)
                docs = loader.load()
                documents.extend(docs)
            
            else:
                # For other file types, try to read as text
                try:
                    with open(project_file.file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    doc = Document(
                        page_content=content,
                        metadata={
                            "source": project_file.file_path,
                            "filename": project_file.name,
                            "file_type": project_file.file_type
                        }
                    )
                    documents.append(doc)
                except Exception:
                    logging.warning(f"Could not process file: {project_file.name}")
                    return []
            
            # Split documents into chunks
            split_docs = []
            for doc in documents:
                chunks = self.text_splitter.split_documents([doc])
                for chunk in chunks:
                    # Add file metadata to chunk
                    chunk.metadata.update({
                        "file_id": project_file.id,
                        "filename": project_file.name,
                        "file_type": project_file.file_type,
                        "chunk_id": hashlib.md5(chunk.page_content.encode()).hexdigest()
                    })
                split_docs.extend(chunks)
            
            return split_docs
            
        except Exception as e:
            logging.error(f"Error loading file {project_file.name}: {e}")
            return []
    
    def _add_documents(self, documents: List[Document], project_id: str):
        """Add documents to FAISS index"""
        if not documents:
            return
        
        # Generate embeddings
        texts = [doc.page_content for doc in documents]
        embeddings = self.embeddings_model.encode(texts)
        
        # Initialize index if needed
        if self.index is None:
            dimension = embeddings.shape[1]
            self.index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        
        # Add to index
        self.index.add(embeddings.astype('float32'))
        
        # Store documents and metadata
        for i, doc in enumerate(documents):
            doc.metadata['project_id'] = project_id
            doc.metadata['index_id'] = len(self.documents)
            self.documents.append(doc)
            self.document_metadata.append(doc.metadata)
    
    def _retrieve_documents(self, state: RAGState) -> RAGState:
        """Retrieve relevant documents for query"""
        query = state["query"]
        
        if self.index is None or len(self.documents) == 0:
            state["documents"] = []
            state["metadata"] = {"retrieved_count": 0}
            return state
        
        # Generate query embedding
        query_embedding = self.embeddings_model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # Search for similar documents
        k = min(5, len(self.documents))  # Retrieve top 5 or all available
        scores, indices = self.index.search(query_embedding.astype('float32'), k)
        
        # Get relevant documents
        relevant_docs = []
        for i, idx in enumerate(indices[0]):
            if idx != -1 and scores[0][i] > 0.3:  # Similarity threshold
                relevant_docs.append(self.documents[idx])
        
        state["documents"] = relevant_docs
        state["metadata"] = {
            "retrieved_count": len(relevant_docs),
            "scores": scores[0].tolist() if len(scores) > 0 else [],
            "query_embedding_norm": float(np.linalg.norm(query_embedding))
        }
        
        return state
    
    def _generate_context(self, state: RAGState) -> RAGState:
        """Generate context from retrieved documents"""
        documents = state["documents"]
        
        if not documents:
            state["context"] = "No relevant documents found in the knowledge base."
            return state
        
        # Combine document contents
        context_parts = []
        for i, doc in enumerate(documents):
            filename = doc.metadata.get('filename', 'Unknown')
            content = doc.page_content.strip()
            context_parts.append(f"[Document {i+1}: {filename}]\n{content}")
        
        context = "\n\n".join(context_parts)
        state["context"] = context
        
        return state
    
    def _generate_response(self, state: RAGState) -> RAGState:
        """Generate response using context (mock implementation)"""
        query = state["query"]
        context = state["context"]
        
        # Mock response generation (in real implementation, use LLM)
        if not context or context == "No relevant documents found in the knowledge base.":
            response = f"I don't have specific information about '{query}' in the knowledge base. Please upload relevant documents to help me answer your question."
        else:
            response = f"Based on the documents in the knowledge base:\n\n{context}\n\nRegarding your question '{query}', the information above should help answer your query. In a full implementation, this would be processed by an LLM to generate a more natural response."
        
        state["response"] = response
        return state
    
    def query(self, query: str, project_ids: List[str] = None) -> Dict[str, Any]:
        """Query the RAG system"""
        try:
            # Filter documents by project if specified
            if project_ids:
                # Temporarily filter index for specific projects
                # In a production system, you'd want separate indices per project
                pass
            
            # Run workflow
            initial_state = RAGState(
                query=query,
                documents=[],
                context="",
                response="",
                metadata={}
            )
            
            result = self.workflow.invoke(initial_state)
            
            return {
                "query": query,
                "response": result["response"],
                "context": result["context"],
                "documents_used": len(result["documents"]),
                "metadata": result["metadata"]
            }
            
        except Exception as e:
            logging.error(f"Error in RAG query: {e}")
            return {
                "query": query,
                "response": f"Error processing query: {str(e)}",
                "context": "",
                "documents_used": 0,
                "metadata": {"error": str(e)}
            }
    
    def save_index(self):
        """Save FAISS index and metadata"""
        try:
            if self.index is not None:
                # Save FAISS index
                index_path = os.path.join(self.storage_path, "faiss_index.bin")
                faiss.write_index(self.index, index_path)
                
                # Save documents and metadata
                docs_path = os.path.join(self.storage_path, "documents.pkl")
                with open(docs_path, 'wb') as f:
                    pickle.dump({
                        'documents': self.documents,
                        'metadata': self.document_metadata
                    }, f)
                
                print(f"Saved RAG index with {len(self.documents)} documents")
                
        except Exception as e:
            logging.error(f"Error saving index: {e}")
    
    def load_index(self):
        """Load FAISS index and metadata"""
        try:
            index_path = os.path.join(self.storage_path, "faiss_index.bin")
            docs_path = os.path.join(self.storage_path, "documents.pkl")
            
            if os.path.exists(index_path) and os.path.exists(docs_path):
                # Load FAISS index
                self.index = faiss.read_index(index_path)
                
                # Load documents and metadata
                with open(docs_path, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data['documents']
                    self.document_metadata = data['metadata']
                
                print(f"Loaded RAG index with {len(self.documents)} documents")
                
        except Exception as e:
            logging.error(f"Error loading index: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get RAG system statistics"""
        return {
            "total_documents": len(self.documents),
            "index_size": self.index.ntotal if self.index else 0,
            "storage_path": self.storage_path,
            "embeddings_model": "all-MiniLM-L6-v2"
        }
