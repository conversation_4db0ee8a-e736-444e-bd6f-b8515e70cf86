"""
Secrets Manager Module

Handles secure storage and management of API keys and secrets
"""

import json
import os
from typing import List, Dict, Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>
from datetime import datetime
import keyring

try:
    # Try relative imports first (when used as module)
    from .models.secret import Secret, APIKey
except ImportError:
    # Fall back to absolute imports (when used in tests)
    from models.secret import Secret, APIKey


class SecretsManager:
    """Manager for API keys and secrets with encryption and secure storage"""
    
    def __init__(self, app_name: str = "HyperCollaborativeAgents"):
        self.app_name = app_name
        self.secrets_file = os.path.join(os.path.expanduser("~"), f".{app_name.lower()}_secrets.json")
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        self.secrets: Dict[str, Secret] = {}
        self.load_secrets()
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key using system keyring"""
        key_name = f"{self.app_name}_encryption_key"
        
        try:
            # Try to get existing key from keyring
            key_str = keyring.get_password(self.app_name, key_name)
            if key_str:
                return key_str.encode()
        except Exception:
            pass
        
        # Generate new key
        key = Fernet.generate_key()
        
        try:
            # Store key in system keyring
            keyring.set_password(self.app_name, key_name, key.decode())
        except Exception:
            # If keyring fails, we'll use the key in memory only
            # In production, you might want to handle this differently
            pass
        
        return key
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
    
    def add_secret(self, secret: Secret) -> bool:
        """Add a new secret"""
        try:
            # Encrypt API keys
            for api_key in secret.api_keys:
                api_key.key = self.encrypt_data(api_key.key)
            
            self.secrets[secret.name] = secret
            self.save_secrets()
            return True
        except Exception as e:
            print(f"Error adding secret: {e}")
            return False
    
    def get_secret(self, name: str) -> Optional[Secret]:
        """Get a secret by name with decrypted API keys"""
        secret = self.secrets.get(name)
        if secret:
            # Create a copy with decrypted keys
            decrypted_secret = Secret(
                name=secret.name,
                provider=secret.provider,
                api_keys=[],
                rotation_strategy=secret.rotation_strategy,
                created_at=secret.created_at,
                updated_at=secret.updated_at
            )
            
            # Decrypt API keys
            for api_key in secret.api_keys:
                decrypted_key = APIKey(
                    key=self.decrypt_data(api_key.key),
                    name=api_key.name,
                    created_at=api_key.created_at,
                    last_used=api_key.last_used,
                    usage_count=api_key.usage_count,
                    is_active=api_key.is_active
                )
                decrypted_secret.api_keys.append(decrypted_key)
            
            return decrypted_secret
        return None
    
    def update_secret(self, secret: Secret) -> bool:
        """Update an existing secret"""
        if secret.name in self.secrets:
            try:
                # Encrypt API keys
                for api_key in secret.api_keys:
                    if not self._is_encrypted(api_key.key):
                        api_key.key = self.encrypt_data(api_key.key)
                
                secret.updated_at = datetime.now()
                self.secrets[secret.name] = secret
                self.save_secrets()
                return True
            except Exception as e:
                print(f"Error updating secret: {e}")
                return False
        return False
    
    def delete_secret(self, name: str) -> bool:
        """Delete a secret"""
        if name in self.secrets:
            del self.secrets[name]
            self.save_secrets()
            return True
        return False
    
    def list_secrets(self) -> List[str]:
        """List all secret names"""
        return list(self.secrets.keys())
    
    def get_all_secrets(self) -> List[Secret]:
        """Get all secrets (with encrypted keys for storage)"""
        return list(self.secrets.values())
    
    def get_next_api_key(self, secret_name: str) -> Optional[str]:
        """Get the next API key for a secret using rotation strategy"""
        secret = self.get_secret(secret_name)
        if secret:
            api_key = secret.get_next_key()
            if api_key:
                # Update usage statistics
                api_key.last_used = datetime.now()
                api_key.usage_count += 1
                
                # Update the stored secret with new usage stats
                stored_secret = self.secrets[secret_name]
                for stored_key in stored_secret.api_keys:
                    if self.decrypt_data(stored_key.key) == api_key.key:
                        stored_key.last_used = api_key.last_used
                        stored_key.usage_count = api_key.usage_count
                        break
                
                self.save_secrets()
                return api_key.key
        return None
    
    def _is_encrypted(self, data: str) -> bool:
        """Check if data is already encrypted"""
        try:
            self.decrypt_data(data)
            return True
        except Exception:
            return False
    
    def save_secrets(self):
        """Save secrets to encrypted file"""
        try:
            # Convert secrets to dictionary format
            secrets_data = {}
            for name, secret in self.secrets.items():
                secrets_data[name] = {
                    "name": secret.name,
                    "provider": secret.provider,
                    "rotation_strategy": secret.rotation_strategy,
                    "created_at": secret.created_at.isoformat(),
                    "updated_at": secret.updated_at.isoformat(),
                    "api_keys": []
                }
                
                for api_key in secret.api_keys:
                    secrets_data[name]["api_keys"].append({
                        "key": api_key.key,  # Already encrypted
                        "name": api_key.name,
                        "created_at": api_key.created_at.isoformat(),
                        "last_used": api_key.last_used.isoformat() if api_key.last_used else None,
                        "usage_count": api_key.usage_count,
                        "is_active": api_key.is_active
                    })
            
            # Save to file
            with open(self.secrets_file, 'w') as f:
                json.dump(secrets_data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving secrets: {e}")
    
    def load_secrets(self):
        """Load secrets from encrypted file"""
        if not os.path.exists(self.secrets_file):
            return
        
        try:
            with open(self.secrets_file, 'r') as f:
                secrets_data = json.load(f)
            
            for name, data in secrets_data.items():
                api_keys = []
                for key_data in data.get("api_keys", []):
                    api_key = APIKey(
                        key=key_data["key"],  # Already encrypted
                        name=key_data.get("name"),
                        created_at=datetime.fromisoformat(key_data["created_at"]),
                        last_used=datetime.fromisoformat(key_data["last_used"]) if key_data.get("last_used") else None,
                        usage_count=key_data.get("usage_count", 0),
                        is_active=key_data.get("is_active", True)
                    )
                    api_keys.append(api_key)
                
                secret = Secret(
                    name=data["name"],
                    provider=data["provider"],
                    api_keys=api_keys,
                    rotation_strategy=data.get("rotation_strategy", "round_robin"),
                    created_at=datetime.fromisoformat(data["created_at"]),
                    updated_at=datetime.fromisoformat(data["updated_at"])
                )
                
                self.secrets[name] = secret
                
        except Exception as e:
            print(f"Error loading secrets: {e}")
    
    def export_secrets(self, export_path: str, include_keys: bool = False) -> bool:
        """Export secrets to a file (optionally without keys for backup)"""
        try:
            export_data = {}
            
            for name, secret in self.secrets.items():
                secret_data = {
                    "name": secret.name,
                    "provider": secret.provider,
                    "rotation_strategy": secret.rotation_strategy,
                    "created_at": secret.created_at.isoformat(),
                    "updated_at": secret.updated_at.isoformat(),
                    "api_keys_count": len(secret.api_keys)
                }
                
                if include_keys:
                    # Decrypt keys for export (use with caution!)
                    decrypted_secret = self.get_secret(name)
                    secret_data["api_keys"] = []
                    for api_key in decrypted_secret.api_keys:
                        secret_data["api_keys"].append({
                            "name": api_key.name,
                            "key": api_key.key,  # Decrypted
                            "created_at": api_key.created_at.isoformat(),
                            "last_used": api_key.last_used.isoformat() if api_key.last_used else None,
                            "usage_count": api_key.usage_count,
                            "is_active": api_key.is_active
                        })
                
                export_data[name] = secret_data
            
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error exporting secrets: {e}")
            return False
    
    def get_secrets_stats(self) -> Dict[str, any]:
        """Get statistics about secrets"""
        stats = {
            "total_secrets": len(self.secrets),
            "total_api_keys": 0,
            "active_api_keys": 0,
            "providers": {},
            "rotation_strategies": {}
        }
        
        for secret in self.secrets.values():
            stats["total_api_keys"] += len(secret.api_keys)
            stats["active_api_keys"] += len([k for k in secret.api_keys if k.is_active])
            
            # Provider stats
            if secret.provider not in stats["providers"]:
                stats["providers"][secret.provider] = 0
            stats["providers"][secret.provider] += 1
            
            # Rotation strategy stats
            if secret.rotation_strategy not in stats["rotation_strategies"]:
                stats["rotation_strategies"][secret.rotation_strategy] = 0
            stats["rotation_strategies"][secret.rotation_strategy] += 1
        
        return stats
