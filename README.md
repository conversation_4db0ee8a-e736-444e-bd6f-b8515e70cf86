# Hyper Collaborative Agents

A lean, clean LangGraph and PyQt application for AI agent collaboration, decision-making, and creative work.

## Features

### 🤖 Agent Builder
- Create agents with names, descriptions, system prompts, and configurations
- Configure tools, capabilities, tone adjustment, and paraphrase randomization
- Select from pre-configured LLM setups
- Load and edit existing agents

### ⚙️ Settings Modal
- **LLM Providers**: Configure Ollama, Groq, OpenAI, <PERSON>, <PERSON>
- **Secrets Manager**: Manage API keys with encryption and rotation
- **Clean UI**: Settings organized in modal dialog

### 🔗 LLM Provider Management
- **Ollama**: Local LLM with URL/port config and model discovery
- **Groq**: High-speed inference with rate limit handling
- **OpenAI, Claude, Gemini**: Cloud provider support
- **Named Configurations**: Create reusable LLM configurations
- **Connection Testing**: Validate configurations before use

### 🔐 Advanced Secrets Management
- **Multi-Key Support**: Multiple API keys per provider
- **Automatic Rotation**: Round-robin, random, and least-recent strategies
- **Encrypted Storage**: Secure local storage with system keyring
- **Usage Tracking**: Monitor key performance and usage
- **Real-time Updates**: Keys available immediately across the app

### 🧪 Agent Testing
- Interactive testing with custom prompts
- Quick test scenarios and conversation history
- Performance metrics and response analysis
- Realistic mock responses for development

### 💾 Persistent Storage
- **SQLite Database**: All configurations stored locally
- **Encrypted Secrets**: API keys encrypted with Fernet + system keyring
- **Automatic Loading**: All data restored on application restart
- **Data Location**: `~/.hyper_collaborative_agents.db` and `~/.hyper_collaborative_agents_secrets.json`

## Installation

### Prerequisites
- Python 3.12 or higher
- Git (already available in your setup)

### Setup

1. **Clone or navigate to the project directory**:
   ```bash
   cd C:/Users/<USER>/IdeaProjects/Langbots
   ```

2. **Activate the virtual environment**:
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python main.py
   ```

## Usage

### Quick Start

1. **Launch**: `python main.py`

2. **Open Settings**: Click "⚙️ Settings" button

3. **Setup Secrets**: Add API keys in "Secrets Manager" tab

4. **Configure LLMs**: Create named configurations in "LLM Providers" tab

5. **Build Agents**: Create agents using your LLM configs

6. **Test**: Validate agents in "Agent Testing" tab

### Key Workflows

#### LLM Configuration Workflow
1. Create named LLM configurations in "LLM Providers"
2. Select these configurations when building agents
3. Reuse configurations across multiple agents

#### API Key Management
- Add multiple keys per provider for rate limit handling
- Automatic rotation: round-robin, random, or least-recent
- Real-time usage tracking and statistics

## Architecture

### Core Components
- **Database** (`src/database.py`): SQLite storage for all data
- **Main Window** (`src/main_window.py`): Central application interface
- **Agent Builder** (`src/ui/agent_builder_widget.py`): Agent creation
- **LLM Providers** (`src/ui/llm_config_widget.py`): LLM configuration management
- **Secrets Manager** (`src/ui/secrets_widget.py`): API key management
- **Agent Tester** (`src/ui/agent_test_widget.py`): Agent testing interface

### Data Storage
- **SQLite Database**: Persistent local storage in user's home directory
- **Encrypted Secrets**: API keys encrypted with Fernet + system keyring
- **Automatic Backup**: All configurations saved automatically

## Project Structure
```
Langbots/
├── main.py                 # Application entry point
├── requirements.txt        # Dependencies
├── .gitignore             # Git ignore rules
├── src/                   # Source code
│   ├── database.py        # SQLite storage
│   ├── secrets_manager.py # Encrypted secrets management
│   ├── main_window.py     # Main application
│   ├── models/            # Data models
│   │   ├── agent.py
│   │   ├── llm_config.py
│   │   └── secret.py
│   └── ui/                # UI components
│       ├── agent_builder_widget.py
│       ├── agent_test_widget.py
│       ├── llm_config_widget.py
│       ├── secrets_widget.py
│       └── settings_modal.py
├── tests/                 # Pytest test suite
│   ├── conftest.py
│   ├── test_models.py
│   └── test_database.py
└── venv/                  # Virtual environment (ignored)
```

## Testing

```bash
# Install test dependencies
pip install pytest pytest-qt

# Run all tests
pytest tests/

# Run with verbose output
pytest tests/ -v

# Run specific test file
pytest tests/test_models.py
```

## Roadmap

### ✅ Phase 1 (Complete)
- Agent creation and configuration
- Multi-LLM provider support
- Encrypted secrets management
- SQLite persistent storage
- Agent testing interface

### 🔄 Phase 2 (Next)
- LangGraph agent workflows
- Agent-to-agent communication
- Advanced collaboration patterns
- Workflow designer

---

**Hyper Collaborative Agents** - Lean, clean AI agent collaboration platform.
